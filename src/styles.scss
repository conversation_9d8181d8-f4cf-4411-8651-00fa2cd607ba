/* You can add global styles to this file, and also import other style files */

// Global mobile optimizations
* {
  box-sizing: border-box;
}

// Prevent iOS zoom on input focus
input, select, textarea {
  font-size: 16px !important; // Must be exactly 16px to prevent zoom
}

// Optimize touch interactions
button, a, [role="button"] {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

// Full dark theme - remove all white backgrounds
html, body {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  min-height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  background: #000000 !important; // Pure black background
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// Reset margins and padding, but preserve necessary backgrounds
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// Only remove white backgrounds from specific elements that shouldn't have them
body, html, app-root {
  background-color: #000000 !important;
}

// Ensure app root takes full viewport
app-root {
  display: block;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  background: #000000 !important;
}

// Container elements should take full space with proper spacing
.home-container, .auth-container, .form-container {
  width: 100%;
  min-height: 100vh;
  background: #000000 !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
  top: 0 !important;
}

// Remove excessive global spacing rules - let individual components handle their own spacing

// Smooth scrolling for better mobile experience
html {
  scroll-behavior: smooth;
}

// Optimize for mobile performance
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Fix iOS Safari viewport issues
@supports (-webkit-touch-callout: none) {
  .auth-container,
  .home-container,
  .form-container {
    min-height: -webkit-fill-available;
  }
}

// Ensure proper touch targets on all interactive elements
button, a, input, select, textarea, [role="button"], [tabindex] {
  min-height: 44px;
  min-width: 44px;
}

// Prevent text selection on UI elements
.nav-link, .nav-button, .social-btn, .step-indicator, .cta-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
