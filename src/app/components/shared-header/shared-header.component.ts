import { Component, Input } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-shared-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <header class="header">
      <div class="logo-section">
        <div class="logo-wrapper">
          <img src="/insphera-logo.png" alt="InspheraAI Logo" class="header-logo" />
        </div>
        <h1 class="app-title">{{ appTitle }}</h1>
      </div>
      
      <nav class="header-nav" *ngIf="showNavigation">
        <!-- Home page navigation -->
        <ng-container *ngIf="pageType === 'home'">
          <button class="nav-link" (click)="navigateToAccount($event)">
            <span class="nav-text">Account</span>
            <div class="nav-glow"></div>
          </button>
        </ng-container>
        
        <!-- Auth pages navigation -->
        <ng-container *ngIf="pageType === 'auth'">
          <button class="back-button" (click)="navigateBack()" (touchstart)="navigateBack()">
            <span class="back-icon">←</span>
            <span class="back-text">Back to Home</span>
          </button>
        </ng-container>
      </nav>
    </header>
  `,
  styleUrls: ['./shared-header.component.scss']
})
export class SharedHeaderComponent {
  @Input() pageType: 'home' | 'auth' | 'dashboard' = 'home';
  @Input() appTitle: string = 'InspheraAI';
  @Input() showNavigation: boolean = true;

  constructor(private router: Router) {}

  navigateToAccount(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    console.log('Navigating to login page...');
    this.router.navigate(['/login']);
  }

  navigateBack(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    console.log('Navigating back to home...');
    this.router.navigate(['/home']);
  }
}
