// Shared Header Component - Compact version for dashboard
// Header styling
.header {
  padding: 1.2rem 3rem; // Reduced from 2rem for more compact header
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  min-height: 70px; // Reduced from default for compact design

  // Ensure header doesn't break on mobile
  @media (max-width: 768px) {
    flex-wrap: nowrap;
    overflow: visible;
    padding: 1rem 2rem; // Even more compact on mobile
    min-height: 60px;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

// Header navigation
.header-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  position: relative;
  padding: 0.8rem 1.8rem;
  border-radius: 50px;
  text-decoration: none;
  font-family: inherit;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: visible; // Allow full clickable area
  cursor: pointer;
  pointer-events: auto !important;
  z-index: 9999 !important; // Maximum z-index to ensure always clickable
  position: relative;
  isolation: isolate; // Create new stacking context
  display: inline-flex; // Better control over clickable area
  align-items: center;
  justify-content: center;
  min-width: 120px; // Ensure adequate clickable width
  min-height: 48px; // Ensure adequate clickable height
  box-sizing: border-box; // Include padding in dimensions

  // Enhanced touch interaction
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;

  // Default (Login) styling - Completely transparent with visible border
  background: transparent !important;
  border: 2px solid rgba(255, 255, 255, 0.5); // Thicker, more visible border
  color: rgba(255, 255, 255, 0.95);

  // Enhanced mobile visibility
  @media (max-width: 768px) {
    border: 2px solid rgba(255, 255, 255, 0.7); // Even more visible on mobile
    min-height: 48px; // Ensure touch target
    font-weight: 600; // Bolder text for better visibility
  }

  // Primary (Register) styling
  &.primary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }

  &:hover {
    transform: translateY(-2px);

    &:not(.primary) {
      background: transparent !important; // Keep transparent on hover
      border-color: rgba(255, 255, 255, 0.9); // Brighter border on hover
      color: #ffffff;
      box-shadow: 0 4px 20px rgba(255, 255, 255, 0.15); // Subtle glow effect
    }

    &.primary {
      background: transparent !important; // Keep transparent on hover
      border-color: rgba(255, 255, 255, 0.9); // Brighter border on hover
      box-shadow: 0 4px 20px rgba(255, 255, 255, 0.15); // Subtle glow effect
    }

    .nav-glow {
      opacity: 1;
    }
  }

  // Ensure clickability on all devices
  &:active, &:focus {
    outline: none;
    background: transparent !important; // Keep transparent on active/focus
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
  }
}

.nav-text {
  position: relative;
  z-index: 2;
}

.nav-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.logo-wrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 4s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.header-logo {
  width: 45px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 15px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 30px rgba(255, 255, 255, 0.2));
  transition: filter 0.3s ease;

  &:hover {
    filter:
      brightness(0)
      invert(1)
      drop-shadow(0 0 20px rgba(255, 255, 255, 0.6))
      drop-shadow(0 0 40px rgba(255, 255, 255, 0.3));
  }
}

.app-title {
  font-size: 1.8rem;
  font-weight: 200;
  margin: 0;
  letter-spacing: 0.08em;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

// Back button styling (for auth pages) - matching home page theme
.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-decoration: none;
  font-family: inherit;
  letter-spacing: 0.02em;
  min-height: 48px;
  min-width: 140px; // Ensure adequate clickable width
  pointer-events: auto !important;
  z-index: 9999 !important;
  position: relative;
  isolation: isolate;
  box-sizing: border-box; // Include padding in dimensions
  overflow: visible; // Allow full clickable area

  // Enhanced touch interaction
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;

  &:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.9);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.15);
  }

  &:active, &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
  }

  .back-icon {
    font-size: 1.2rem;
    line-height: 1;
  }

  .back-text {
    font-size: 0.9rem;
  }
}

// Logo glow animation (same as home page)
@keyframes logoGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

// Mobile responsive styles - exactly matching home page
@media (max-width: 768px) {
  .header {
    padding: 1rem 1.5rem;
    flex-direction: row; // Keep horizontal layout for mobile
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .logo-section {
    flex-shrink: 0; // Prevent logo from shrinking
  }

  .app-title {
    font-size: 1.2rem; // Smaller title on mobile
  }

  .header-nav {
    gap: 0.8rem;
    flex-wrap: nowrap; // Keep buttons in one line
    justify-content: flex-end;
    flex-shrink: 0; // Prevent nav from shrinking
  }

  .nav-link {
    padding: 1rem 1.5rem; // Larger touch area for mobile
    min-width: 120px; // Adequate touch-friendly width
    min-height: 50px; // Touch-friendly height (above 44px minimum)
    position: relative;
    z-index: 9999 !important; // Maximum z-index to ensure clickable
    touch-action: manipulation; // Optimize for touch
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem; // Readable text for mobile
    border: 2px solid rgba(255, 255, 255, 0.8) !important; // Very visible border
    background: transparent !important; // No background
    cursor: pointer;
    text-decoration: none;
    pointer-events: auto !important;
    isolation: isolate; // Create new stacking context
    box-sizing: border-box; // Include padding in dimensions
    overflow: visible; // Allow full clickable area

    // Enhanced mobile interaction
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      transform: none; // Disable transform on mobile
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background
      border-color: rgba(255, 255, 255, 0.7) !important;

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
        border-color: rgba(255, 255, 255, 0.95) !important;
      }
    }
  }

  .back-button {
    padding: 1rem 1.5rem;
    min-height: 50px;
    min-width: 140px;
    font-size: 0.9rem;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    box-sizing: border-box;
    overflow: visible;

    &:hover, &:active, &:focus {
      transform: none;
      border-color: rgba(255, 255, 255, 0.95) !important;
    }
  }
}

// Small Mobile Optimization - exactly matching home page
@media (max-width: 480px) {
  .header {
    padding: 0.8rem 1rem;
    flex-direction: row; // Keep horizontal
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    gap: 0.8rem; // Reduce gap between logo and title
  }

  .app-title {
    font-size: 1rem; // Even smaller on small screens
  }

  .header-nav {
    gap: 0.5rem; // Tighter spacing
  }

  .nav-link {
    padding: 0.9rem 1.2rem; // Adequate touch area
    min-width: 90px; // Touch-friendly width
    min-height: 44px; // Minimum touch target
    font-size: 0.85rem;
    position: relative;
    z-index: 9999 !important; // Maximum z-index
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
    background: transparent !important; // No background
    pointer-events: auto !important;
    isolation: isolate;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
      }
    }
  }

  .back-button {
    padding: 0.9rem 1.2rem;
    min-height: 44px;
    font-size: 0.85rem;
    border: 2px solid rgba(255, 255, 255, 0.7) !important;

    &:hover, &:active, &:focus {
      transform: none;
      border-color: rgba(255, 255, 255, 0.95) !important;
    }
  }
}

// Extra Small Mobile - exactly matching home page
@media (max-width: 320px) {
  .header {
    padding: 0.6rem 0.8rem;
    flex-direction: row; // Keep horizontal
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    gap: 0.5rem;
  }

  .app-title {
    font-size: 0.9rem; // Very compact
  }

  .header-nav {
    gap: 0.3rem; // Very tight spacing
  }

  .nav-link {
    padding: 0.8rem 1rem; // Maintain touch-friendly size
    min-width: 80px; // Adequate touch target even on small screens
    min-height: 44px; // Maintain minimum touch target
    font-size: 0.8rem;
    position: relative;
    z-index: 9999 !important; // Maximum z-index
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
    background: transparent !important; // No background
    pointer-events: auto !important;
    isolation: isolate;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
      }
    }
  }

  .back-button {
    padding: 0.8rem 1rem;
    min-height: 44px;
    font-size: 0.8rem;
    border: 2px solid rgba(255, 255, 255, 0.7) !important;

    &:hover, &:active, &:focus {
      transform: none;
      border-color: rgba(255, 255, 255, 0.95) !important;
    }
  }
}
