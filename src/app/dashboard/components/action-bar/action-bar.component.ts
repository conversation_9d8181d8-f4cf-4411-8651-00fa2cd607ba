import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-action-bar',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div class="action-bar">
      <button class="action-btn primary" (click)="addKpi.emit()">
        <mat-icon>add</mat-icon>
        Add KPI
      </button>
      <button class="action-btn" (click)="toggleTemplates.emit()">
        <mat-icon>dashboard_customize</mat-icon>
        Templates
      </button>
      <button class="action-btn" (click)="editLayout.emit()">
        <mat-icon>edit</mat-icon>
        Edit Layout
      </button>
      <button class="action-btn" (click)="saveDashboard.emit()">
        <mat-icon>save</mat-icon>
        Save Dashboard
      </button>
    </div>
  `,
  styleUrls: ['./action-bar.component.scss']
})
export class ActionBarComponent {
  @Output() addKpi = new EventEmitter<void>();
  @Output() toggleTemplates = new EventEmitter<void>();
  @Output() editLayout = new EventEmitter<void>();
  @Output() saveDashboard = new EventEmitter<void>();
}
