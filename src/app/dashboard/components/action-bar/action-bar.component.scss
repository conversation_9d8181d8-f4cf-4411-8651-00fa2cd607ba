// Action Bar Component Styles
.action-bar {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  min-height: 44px; // Touch-friendly
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }
  
  &.primary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    
    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.12) 100%);
      border-color: rgba(255, 255, 255, 0.4);
    }
  }
  
  mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .action-bar {
    gap: 0.75rem;
  }
  
  .action-btn {
    padding: 0.8rem 1rem;
    font-size: 0.85rem;
    flex: 1;
    min-width: 120px;
    justify-content: center;
    
    mat-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
    }
  }
}

@media (max-width: 480px) {
  .action-bar {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
