import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

export interface TemplateCategory {
  id: string;
  name: string;
  icon: string;
}

export interface KpiTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  preview: Array<{icon: string, label: string}>;
}

@Component({
  selector: 'app-templates-section',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div class="templates-section" [class.expanded]="showTemplates">
      <div class="templates-header">
        <h3>KPI Templates</h3>
        <button class="collapse-btn" (click)="onToggleTemplates()">
          <mat-icon>{{ showTemplates ? 'expand_less' : 'expand_more' }}</mat-icon>
        </button>
      </div>
      <div class="templates-content" *ngIf="showTemplates">
        <div class="template-categories">
          <button 
            *ngFor="let category of templateCategories" 
            class="category-btn"
            [class.active]="activeTemplateCategory === category.id"
            (click)="onSelectCategory(category.id)"
          >
            <mat-icon>{{ category.icon }}</mat-icon>
            {{ category.name }}
          </button>
        </div>
        <div class="template-grid">
          <div 
            *ngFor="let template of getFilteredTemplates()" 
            class="template-card"
            (click)="onApplyTemplate(template)"
          >
            <div class="template-preview">
              <div class="preview-kpis">
                <div *ngFor="let kpi of template.preview" class="preview-kpi">
                  <mat-icon>{{ kpi.icon }}</mat-icon>
                  <span>{{ kpi.label }}</span>
                </div>
              </div>
            </div>
            <div class="template-info">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
              <button class="apply-btn">
                <mat-icon>download</mat-icon>
                Apply Template
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./templates-section.component.scss']
})
export class TemplatesSectionComponent {
  @Input() showTemplates: boolean = false;
  @Input() activeTemplateCategory: string = 'sales';
  @Input() templateCategories: TemplateCategory[] = [];
  @Input() kpiTemplates: KpiTemplate[] = [];
  
  @Output() toggleTemplates = new EventEmitter<void>();
  @Output() selectCategory = new EventEmitter<string>();
  @Output() applyTemplate = new EventEmitter<KpiTemplate>();

  onToggleTemplates(): void {
    this.toggleTemplates.emit();
  }

  onSelectCategory(categoryId: string): void {
    this.selectCategory.emit(categoryId);
  }

  onApplyTemplate(template: KpiTemplate): void {
    this.applyTemplate.emit(template);
  }

  getFilteredTemplates(): KpiTemplate[] {
    return this.kpiTemplates.filter(template => template.category === this.activeTemplateCategory);
  }
}
