// Templates Section Component Styles
.templates-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-top: 2rem;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.expanded {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
  }
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
  }
}

.collapse-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }
  
  mat-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

.templates-content {
  padding: 1.5rem;
}

.template-categories {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  padding: 0.6rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }
  
  mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.template-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }
}

.template-preview {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.preview-kpis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.preview-kpi {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  
  mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    color: rgba(255, 255, 255, 0.6);
  }
}

.template-info {
  padding: 1rem;
  
  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff;
  }
  
  p {
    margin: 0 0 1rem 0;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
  }
}

.apply-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  width: 100%;
  justify-content: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }
  
  mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .template-categories {
    gap: 0.5rem;
  }
  
  .category-btn {
    padding: 0.7rem 0.8rem;
    font-size: 0.8rem;
    min-height: 44px; // Touch-friendly
  }
}
