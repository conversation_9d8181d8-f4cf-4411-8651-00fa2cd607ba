<div class="kpi-builder-overlay" *ngIf="isOpen" (click)="onClose()">
  <div class="kpi-builder-modal" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="modal-header">
      <h2>Create Custom KPI</h2>
      <button class="close-btn" (click)="onClose()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Content -->
    <div class="modal-content">
      <!-- Basic Configuration -->
      <div class="config-section">
        <h3>Basic Configuration</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>KPI Label</mat-label>
            <input matInput [(ngModel)]="config.label" placeholder="Enter KPI name">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Data Source</mat-label>
            <mat-select [(ngModel)]="config.dataSource">
              <mat-option *ngFor="let source of dataSources" [value]="source.id">
                <div class="data-source-option">
                  <mat-icon>{{ source.type === 'api' ? 'api' : source.type === 'database' ? 'storage' : source.type === 'csv' ? 'description' : 'code' }}</mat-icon>
                  <span>{{ source.name }}</span>
                </div>
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Metric Type</mat-label>
            <mat-select [(ngModel)]="config.metricType">
              <mat-option *ngFor="let metric of metricTypes" [value]="metric.id">
                {{ metric.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Aggregation</mat-label>
            <mat-select [(ngModel)]="config.aggregation">
              <mat-option *ngFor="let agg of aggregations" [value]="agg.id">
                {{ agg.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Time Period</mat-label>
            <mat-select [(ngModel)]="config.timePeriod">
              <mat-option *ngFor="let period of timePeriods" [value]="period.id">
                {{ period.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Visual Configuration -->
      <div class="config-section">
        <h3>Visual Configuration</h3>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Icon</mat-label>
            <mat-select [(ngModel)]="config.icon">
              <mat-option *ngFor="let icon of materialIcons" [value]="icon">
                <div class="icon-option">
                  <mat-icon>{{ icon }}</mat-icon>
                  <span>{{ icon }}</span>
                </div>
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Color Scheme</mat-label>
            <mat-select [(ngModel)]="config.colorScheme">
              <mat-option *ngFor="let scheme of colorSchemes" [value]="scheme.id">
                <div class="color-option">
                  <div class="color-preview" [style.background-color]="scheme.color"></div>
                  <span>{{ scheme.name }}</span>
                </div>
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Comparison Period</mat-label>
            <mat-select [(ngModel)]="config.comparisonPeriod">
              <mat-option *ngFor="let period of comparisonPeriods" [value]="period.id">
                {{ period.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Target (Optional)</mat-label>
            <input matInput type="number" [(ngModel)]="config.target" placeholder="Set target value">
          </mat-form-field>
        </div>
      </div>

      <!-- Advanced Options -->
      <div class="config-section">
        <div class="section-header">
          <h3>Advanced Options</h3>
          <mat-slide-toggle [(ngModel)]="showAdvancedOptions">
            Show Advanced
          </mat-slide-toggle>
        </div>

        <div *ngIf="showAdvancedOptions" class="advanced-options">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Custom Query (SQL/API)</mat-label>
            <textarea matInput 
                      [(ngModel)]="config.customQuery" 
                      placeholder="SELECT SUM(revenue) FROM sales WHERE date >= '2024-01-01'"
                      rows="4">
            </textarea>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="modal-footer">
      <button class="cancel-btn" (click)="onClose()">
        <mat-icon>cancel</mat-icon>
        Cancel
      </button>
      <button class="create-btn" 
              [disabled]="!isFormValid()" 
              (click)="onCreateKpi()">
        <mat-icon>add</mat-icon>
        Create KPI
      </button>
    </div>
  </div>
</div>
