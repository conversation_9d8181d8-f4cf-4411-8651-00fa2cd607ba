import { Component, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { KpiCard } from '../kpi-card/kpi-card.component';

export interface DataSource {
  id: string;
  name: string;
  type: 'api' | 'database' | 'csv' | 'custom';
  endpoint?: string;
}

export interface MetricType {
  id: string;
  name: string;
  format: 'currency' | 'percentage' | 'number' | 'decimal';
}

export interface KpiBuilderConfig {
  dataSource: string;
  metricType: string;
  aggregation: string;
  timePeriod: string;
  displayFormat: string;
  colorScheme: string;
  icon: string;
  target?: number;
  comparisonPeriod: string;
  customQuery?: string;
  label: string;
}

@Component({
  selector: 'app-kpi-builder',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule,
    MatIconModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatSlideToggleModule
  ],
  templateUrl: './kpi-builder.component.html',
  styleUrls: ['./kpi-builder.component.scss']
})
export class KpiBuilderComponent {
  @Input() isOpen: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() createKpi = new EventEmitter<KpiCard>();

  config: KpiBuilderConfig = {
    dataSource: '',
    metricType: '',
    aggregation: 'sum',
    timePeriod: 'monthly',
    displayFormat: 'number',
    colorScheme: 'blue',
    icon: 'analytics',
    comparisonPeriod: 'last_month',
    label: ''
  };

  showAdvancedOptions = false;

  dataSources: DataSource[] = [
    { id: 'sales_api', name: 'Sales API', type: 'api', endpoint: '/api/sales' },
    { id: 'customer_db', name: 'Customer Database', type: 'database' },
    { id: 'marketing_csv', name: 'Marketing Data CSV', type: 'csv' },
    { id: 'custom_query', name: 'Custom Query', type: 'custom' }
  ];

  metricTypes: MetricType[] = [
    { id: 'revenue', name: 'Revenue', format: 'currency' },
    { id: 'count', name: 'Count', format: 'number' },
    { id: 'percentage', name: 'Percentage', format: 'percentage' },
    { id: 'average', name: 'Average', format: 'decimal' },
    { id: 'conversion_rate', name: 'Conversion Rate', format: 'percentage' }
  ];

  aggregations = [
    { id: 'sum', name: 'Sum' },
    { id: 'average', name: 'Average' },
    { id: 'count', name: 'Count' },
    { id: 'min', name: 'Minimum' },
    { id: 'max', name: 'Maximum' }
  ];

  timePeriods = [
    { id: 'daily', name: 'Daily' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'monthly', name: 'Monthly' },
    { id: 'quarterly', name: 'Quarterly' },
    { id: 'yearly', name: 'Yearly' }
  ];

  colorSchemes = [
    { id: 'blue', name: 'Blue', color: '#3b82f6' },
    { id: 'green', name: 'Green', color: '#10b981' },
    { id: 'purple', name: 'Purple', color: '#8b5cf6' },
    { id: 'orange', name: 'Orange', color: '#f59e0b' },
    { id: 'red', name: 'Red', color: '#ef4444' }
  ];

  materialIcons = [
    'analytics', 'trending_up', 'trending_down', 'attach_money', 'people',
    'shopping_cart', 'star', 'schedule', 'check_circle', 'campaign',
    'psychology', 'support_agent', 'track_changes', 'visibility'
  ];

  comparisonPeriods = [
    { id: 'last_month', name: 'Last Month' },
    { id: 'last_quarter', name: 'Last Quarter' },
    { id: 'last_year', name: 'Last Year' },
    { id: 'previous_period', name: 'Previous Period' }
  ];

  onClose(): void {
    this.close.emit();
  }

  onCreateKpi(): void {
    // Generate mock data based on configuration
    const mockValue = this.generateMockValue();
    const mockChange = (Math.random() - 0.5) * 20; // Random change between -10% and +10%
    
    const newKpi: KpiCard = {
      id: `custom_${Date.now()}`,
      label: this.config.label || 'Custom KPI',
      value: mockValue,
      icon: this.config.icon,
      change: Math.round(mockChange * 10) / 10,
      comparison: this.getComparisonText(),
      period: this.getTimePeriodText(),
      progress: this.config.target ? Math.floor(Math.random() * 100) : undefined
    };

    this.createKpi.emit(newKpi);
    this.resetForm();
    this.close.emit();
  }

  private generateMockValue(): string {
    const metricType = this.metricTypes.find(m => m.id === this.config.metricType);
    const format = metricType?.format || 'number';

    switch (format) {
      case 'currency':
        return `$${(Math.random() * 100000).toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
      case 'percentage':
        return `${(Math.random() * 100).toFixed(1)}%`;
      case 'decimal':
        return (Math.random() * 1000).toFixed(2);
      default:
        return Math.floor(Math.random() * 10000).toLocaleString();
    }
  }

  private getComparisonText(): string {
    const period = this.comparisonPeriods.find(p => p.id === this.config.comparisonPeriod);
    return period?.name.toLowerCase() || 'last month';
  }

  private getTimePeriodText(): string {
    const period = this.timePeriods.find(p => p.id === this.config.timePeriod);
    return period?.name || 'Monthly';
  }

  private resetForm(): void {
    this.config = {
      dataSource: '',
      metricType: '',
      aggregation: 'sum',
      timePeriod: 'monthly',
      displayFormat: 'number',
      colorScheme: 'blue',
      icon: 'analytics',
      comparisonPeriod: 'last_month',
      label: ''
    };
    this.showAdvancedOptions = false;
  }

  isFormValid(): boolean {
    return !!(this.config.label && this.config.dataSource && this.config.metricType);
  }
}
