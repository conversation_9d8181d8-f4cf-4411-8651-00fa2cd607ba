// KPI Builder Modal Styles
.kpi-builder-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.kpi-builder-modal {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  h2 {
    margin: 0;
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 300;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
}

.close-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }
}

.modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.config-section {
  margin-bottom: 2rem;
  
  h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
}

// Material Form Field Overrides
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background: rgba(255, 255, 255, 0.05) !important;
      border-radius: 8px !important;
    }
    
    .mat-mdc-form-field-outline {
      color: rgba(255, 255, 255, 0.2) !important;
    }
    
    .mat-mdc-form-field-outline-thick {
      color: rgba(255, 255, 255, 0.4) !important;
    }
    
    .mat-mdc-form-field-label {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .mat-mdc-input-element {
      color: #ffffff !important;
    }
    
    .mat-mdc-select-value {
      color: #ffffff !important;
    }
  }
  
  .mat-mdc-select-panel {
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(20px) !important;
  }
  
  .mat-mdc-option {
    color: rgba(255, 255, 255, 0.8) !important;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
    }
    
    &.mdc-list-item--selected {
      background: rgba(255, 255, 255, 0.15) !important;
      color: #ffffff !important;
    }
  }
  
  .mat-mdc-slide-toggle {
    .mdc-switch__track {
      background: rgba(255, 255, 255, 0.2) !important;
    }
    
    &.mat-checked .mdc-switch__track {
      background: rgba(255, 255, 255, 0.4) !important;
    }
  }
}

.data-source-option,
.icon-option,
.color-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.advanced-options {
  margin-top: 1rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.cancel-btn,
.create-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  min-height: 44px;
}

.cancel-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }
}

.create-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.12) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .kpi-builder-modal {
    width: 95%;
    margin: 1rem;
  }
  
  .modal-content {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .half-width {
    width: 100%;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 0.75rem;
    
    .cancel-btn,
    .create-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
