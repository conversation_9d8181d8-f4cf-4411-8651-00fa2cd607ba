// KPI Grid Component Styles - Fixed Sizing
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 280px);
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-content: start;

  // Ensure consistent card sizing
  app-kpi-card {
    width: 280px;
    height: 200px;
  }
}

// Mobile responsive with fixed columns
@media (max-width: 1200px) {
  .kpi-grid {
    grid-template-columns: repeat(3, 280px);
    justify-content: center;
  }
}

@media (max-width: 900px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 280px);
    justify-content: center;
    gap: 1rem;
  }
}

@media (max-width: 600px) {
  .kpi-grid {
    grid-template-columns: 1fr;
    justify-content: stretch;

    app-kpi-card {
      width: 100%;
      max-width: 400px;
      margin: 0 auto;
    }
  }
}
