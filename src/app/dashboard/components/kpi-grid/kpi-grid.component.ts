import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KpiCardComponent, KpiCard } from '../kpi-card/kpi-card.component';

@Component({
  selector: 'app-kpi-grid',
  standalone: true,
  imports: [CommonModule, KpiCardComponent],
  template: `
    <div class="kpi-grid">
      <app-kpi-card 
        *ngFor="let kpi of kpiCards" 
        [kpi]="kpi"
      ></app-kpi-card>
    </div>
  `,
  styleUrls: ['./kpi-grid.component.scss']
})
export class KpiGridComponent {
  @Input() kpiCards: KpiCard[] = [];
}
