import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

export interface KpiCard {
  id: string;
  label: string;
  value: string;
  icon: string;
  change: number;
  comparison: string;
  period: string;
  progress?: number;
}

@Component({
  selector: 'app-kpi-card',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div class="kpi-card" [class.positive]="kpi.change > 0" [class.negative]="kpi.change < 0">
      <div class="kpi-header">
        <mat-icon class="kpi-icon">{{ kpi.icon }}</mat-icon>
        <span class="kpi-period">{{ kpi.period }}</span>
      </div>
      <div class="kpi-content">
        <div class="kpi-value">{{ kpi.value }}</div>
        <div class="kpi-label">{{ kpi.label }}</div>
      </div>
      <div class="kpi-footer">
        <div class="kpi-change" [class.positive]="kpi.change > 0" [class.negative]="kpi.change < 0">
          <mat-icon class="change-icon">{{ kpi.change > 0 ? 'trending_up' : 'trending_down' }}</mat-icon>
          <span>{{ Math.abs(kpi.change) }}%</span>
        </div>
        <div class="kpi-comparison">vs {{ kpi.comparison }}</div>
      </div>
      <div class="kpi-progress" *ngIf="kpi.progress">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="kpi.progress"></div>
        </div>
        <span class="progress-text">{{ kpi.progress }}% of target</span>
      </div>
    </div>
  `,
  styleUrls: ['./kpi-card.component.scss']
})
export class KpiCardComponent {
  @Input() kpi!: KpiCard;
  
  Math = Math;
}
