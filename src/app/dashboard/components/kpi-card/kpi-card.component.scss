// KPI Card Component Styles - Fixed Dimensions
.kpi-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  &.positive {
    border-left: 3px solid #10b981;
  }
  
  &.negative {
    border-left: 3px solid #ef4444;
  }
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.kpi-icon {
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
  color: rgba(255, 255, 255, 0.8) !important;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.kpi-period {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.kpi-content {
  margin-bottom: 1rem;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 0.25rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.kpi-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.kpi-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.kpi-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  font-weight: 500;
  
  &.positive {
    color: #10b981;
  }
  
  &.negative {
    color: #ef4444;
  }
}

.change-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.kpi-comparison {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.kpi-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

// Mobile responsive
@media (max-width: 768px) {
  .kpi-card {
    padding: 1rem;
  }
  
  .kpi-value {
    font-size: 1.5rem;
  }
  
  .kpi-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}
