// Edit Layout Component Styles
.edit-layout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.edit-layout-panel {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  flex-direction: column;
}

.edit-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  h3 {
    margin: 0;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 300;
  }
}

.toolbar-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tool-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  min-height: 36px;
  
  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
  }
  
  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
  
  mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}

.separator {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 0.5rem;
}

.toolbar-actions {
  display: flex;
  gap: 1rem;
}

.cancel-btn,
.save-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  min-height: 44px;
}

.cancel-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }
}

.save-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  
  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.12) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
  }
}

.edit-grid {
  flex: 1;
  position: relative;
  overflow: auto;
  padding: 2rem;
  
  &.grid-snap {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
}

.kpi-drop-list {
  min-height: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, 280px);
  gap: 1.5rem;
  justify-content: start;
}

.draggable-kpi {
  position: relative;
  width: 280px;
  height: 200px;
  cursor: move;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &.selected {
    z-index: 10;
  }
  
  &.cdk-drag-dragging {
    transform: rotate(5deg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  }
}

.kpi-preview {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  backdrop-filter: blur(10px);
  
  .kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    mat-icon {
      font-size: 20px !important;
      width: 20px !important;
      height: 20px !important;
      color: rgba(255, 255, 255, 0.8);
    }
    
    span {
      font-size: 0.75rem;
      color: rgba(255, 255, 255, 0.6);
    }
  }
  
  .kpi-value {
    font-size: 1.5rem;
    font-weight: 300;
    color: #ffffff;
    margin-bottom: 0.5rem;
  }
  
  .kpi-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
  }
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 14px;
  pointer-events: none;
  animation: pulse 2s infinite;
}

.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
  
  &.nw {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
  }
  
  &.ne {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
  }
  
  &.sw {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
  }
  
  &.se {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
  }
}

.properties-panel {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 250px;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  
  h4 {
    margin: 0 0 1rem 0;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 500;
  }
}

.property-group {
  margin-bottom: 1rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
  }
  
  input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #ffffff;
    padding: 0.5rem;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.4);
      background: rgba(255, 255, 255, 0.08);
    }
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

// Mobile responsive
@media (max-width: 768px) {
  .edit-toolbar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .toolbar-section {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .toolbar-controls {
    flex-wrap: wrap;
  }
  
  .properties-panel {
    position: static;
    transform: none;
    width: 100%;
    margin: 1rem;
  }
  
  .edit-grid {
    padding: 1rem;
  }
  
  .kpi-drop-list {
    grid-template-columns: 1fr;
    justify-content: center;
  }
}
