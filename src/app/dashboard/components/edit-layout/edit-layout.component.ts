import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { KpiCard } from '../kpi-card/kpi-card.component';

export interface LayoutAction {
  type: 'move' | 'resize' | 'add' | 'remove';
  data: any;
}

@Component({
  selector: 'app-edit-layout',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, DragDropModule],
  template: `
    <div class="edit-layout-overlay" *ngIf="isEditMode" (click)="onCancel()">
      <div class="edit-layout-panel" (click)="$event.stopPropagation()">
        <!-- Toolbar -->
        <div class="edit-toolbar">
          <div class="toolbar-section">
            <h3>Edit Layout</h3>
            <div class="toolbar-controls">
              <button class="tool-btn" 
                      [class.active]="gridSnap" 
                      (click)="toggleGridSnap()"
                      title="Grid Snap">
                <mat-icon>grid_on</mat-icon>
              </button>
              
              <div class="separator"></div>
              
              <button class="tool-btn" 
                      (click)="alignItems('left')"
                      title="Align Left">
                <mat-icon>format_align_left</mat-icon>
              </button>
              
              <button class="tool-btn" 
                      (click)="alignItems('center')"
                      title="Align Center">
                <mat-icon>format_align_center</mat-icon>
              </button>
              
              <button class="tool-btn" 
                      (click)="alignItems('right')"
                      title="Align Right">
                <mat-icon>format_align_right</mat-icon>
              </button>
              
              <div class="separator"></div>
              
              <button class="tool-btn" 
                      [disabled]="!canUndo"
                      (click)="undo()"
                      title="Undo">
                <mat-icon>undo</mat-icon>
              </button>
              
              <button class="tool-btn" 
                      [disabled]="!canRedo"
                      (click)="redo()"
                      title="Redo">
                <mat-icon>redo</mat-icon>
              </button>
            </div>
          </div>
          
          <div class="toolbar-actions">
            <button class="cancel-btn" (click)="onCancel()">
              <mat-icon>close</mat-icon>
              Cancel
            </button>
            <button class="save-btn" (click)="onSave()">
              <mat-icon>check</mat-icon>
              Save Layout
            </button>
          </div>
        </div>

        <!-- Edit Grid -->
        <div class="edit-grid" [class.grid-snap]="gridSnap">
          <div class="grid-background" *ngIf="gridSnap"></div>
          
          <div cdkDropList 
               class="kpi-drop-list"
               (cdkDropListDropped)="onDrop($event)">
            <div *ngFor="let kpi of editableKpis; let i = index"
                 cdkDrag
                 class="draggable-kpi"
                 [class.selected]="selectedKpiIndex === i"
                 (click)="selectKpi(i)">
              
              <!-- KPI Content -->
              <div class="kpi-preview">
                <div class="kpi-header">
                  <mat-icon>{{ kpi.icon }}</mat-icon>
                  <span>{{ kpi.period }}</span>
                </div>
                <div class="kpi-value">{{ kpi.value }}</div>
                <div class="kpi-label">{{ kpi.label }}</div>
              </div>
              
              <!-- Resize Handles -->
              <div class="resize-handles" *ngIf="selectedKpiIndex === i">
                <div class="resize-handle nw" (mousedown)="startResize($event, 'nw')"></div>
                <div class="resize-handle ne" (mousedown)="startResize($event, 'ne')"></div>
                <div class="resize-handle sw" (mousedown)="startResize($event, 'sw')"></div>
                <div class="resize-handle se" (mousedown)="startResize($event, 'se')"></div>
              </div>
              
              <!-- Selection Border -->
              <div class="selection-border" *ngIf="selectedKpiIndex === i"></div>
            </div>
          </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel" *ngIf="selectedKpiIndex !== -1">
          <h4>KPI Properties</h4>
          <div class="property-group">
            <label>Width</label>
            <input type="number" 
                   [(ngModel)]="editableKpis[selectedKpiIndex].width" 
                   (change)="onPropertyChange()">
          </div>
          <div class="property-group">
            <label>Height</label>
            <input type="number" 
                   [(ngModel)]="editableKpis[selectedKpiIndex].height" 
                   (change)="onPropertyChange()">
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./edit-layout.component.scss']
})
export class EditLayoutComponent {
  @Input() isEditMode: boolean = false;
  @Input() kpiCards: KpiCard[] = [];
  @Output() saveLayout = new EventEmitter<KpiCard[]>();
  @Output() cancelEdit = new EventEmitter<void>();

  editableKpis: any[] = [];
  selectedKpiIndex: number = -1;
  gridSnap: boolean = true;
  
  // Undo/Redo functionality
  actionHistory: LayoutAction[] = [];
  currentActionIndex: number = -1;
  
  get canUndo(): boolean {
    return this.currentActionIndex >= 0;
  }
  
  get canRedo(): boolean {
    return this.currentActionIndex < this.actionHistory.length - 1;
  }

  ngOnChanges(): void {
    if (this.isEditMode) {
      this.initializeEditMode();
    }
  }

  private initializeEditMode(): void {
    this.editableKpis = this.kpiCards.map((kpi, index) => ({
      ...kpi,
      width: 280,
      height: 200,
      x: (index % 4) * 300,
      y: Math.floor(index / 4) * 220
    }));
    this.selectedKpiIndex = -1;
    this.actionHistory = [];
    this.currentActionIndex = -1;
  }

  onDrop(event: CdkDragDrop<any[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      this.addAction({
        type: 'move',
        data: {
          from: event.previousIndex,
          to: event.currentIndex,
          previousOrder: [...this.editableKpis]
        }
      });
      
      moveItemInArray(this.editableKpis, event.previousIndex, event.currentIndex);
    }
  }

  selectKpi(index: number): void {
    this.selectedKpiIndex = index;
  }

  toggleGridSnap(): void {
    this.gridSnap = !this.gridSnap;
  }

  alignItems(alignment: 'left' | 'center' | 'right'): void {
    if (this.selectedKpiIndex === -1) return;
    
    // Implementation for alignment
    console.log(`Aligning items to ${alignment}`);
  }

  startResize(event: MouseEvent, direction: string): void {
    event.preventDefault();
    event.stopPropagation();
    
    // Implementation for resize functionality
    console.log(`Starting resize in direction: ${direction}`);
  }

  onPropertyChange(): void {
    // Handle property changes
    console.log('Property changed');
  }

  addAction(action: LayoutAction): void {
    // Remove any actions after current index (for redo functionality)
    this.actionHistory = this.actionHistory.slice(0, this.currentActionIndex + 1);
    this.actionHistory.push(action);
    this.currentActionIndex++;
    
    // Limit history size
    if (this.actionHistory.length > 50) {
      this.actionHistory.shift();
      this.currentActionIndex--;
    }
  }

  undo(): void {
    if (!this.canUndo) return;
    
    const action = this.actionHistory[this.currentActionIndex];
    this.revertAction(action);
    this.currentActionIndex--;
  }

  redo(): void {
    if (!this.canRedo) return;
    
    this.currentActionIndex++;
    const action = this.actionHistory[this.currentActionIndex];
    this.applyAction(action);
  }

  private revertAction(action: LayoutAction): void {
    switch (action.type) {
      case 'move':
        this.editableKpis = [...action.data.previousOrder];
        break;
      // Add other action types as needed
    }
  }

  private applyAction(action: LayoutAction): void {
    switch (action.type) {
      case 'move':
        moveItemInArray(this.editableKpis, action.data.from, action.data.to);
        break;
      // Add other action types as needed
    }
  }

  onSave(): void {
    const updatedKpis = this.editableKpis.map(kpi => ({
      id: kpi.id,
      label: kpi.label,
      value: kpi.value,
      icon: kpi.icon,
      change: kpi.change,
      comparison: kpi.comparison,
      period: kpi.period,
      progress: kpi.progress
    }));
    
    this.saveLayout.emit(updatedKpis);
  }

  onCancel(): void {
    this.cancelEdit.emit();
  }
}
