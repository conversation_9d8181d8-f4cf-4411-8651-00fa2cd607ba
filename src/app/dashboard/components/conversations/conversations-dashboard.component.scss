// Conversations Dashboard - Futuristic Dark Theme with Glass-morphism
.conversations-dashboard {
  width: 100%;
  height: 100%;
  background: #000000;
  color: #ffffff;
  position: relative;
  overflow: hidden;

  // Global input styling for consistent design
  ::ng-deep {
    .mat-mdc-form-field {
      margin-bottom: 0.8rem;

      .mat-mdc-text-field-wrapper {
        border-radius: 12px !important;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.03) !important;
      }

      .mdc-text-field--outlined .mdc-notched-outline {
        border-color: rgba(255, 255, 255, 0.15) !important;
        border-radius: 12px !important;
      }

      .mdc-text-field--focused .mdc-notched-outline {
        border-color: rgba(33, 150, 243, 0.3) !important;
        box-shadow: 0 0 8px rgba(33, 150, 243, 0.2) !important;
      }

      input, textarea, .mat-mdc-select-value {
        color: #ffffff !important;
        padding: 1rem !important;
      }

      .mat-mdc-form-field-infix {
        padding: 1rem !important;
        min-height: auto !important;
      }

      .mat-mdc-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }
    }

    .mat-mdc-select-panel {
      background: rgba(0, 0, 0, 0.95) !important;
      backdrop-filter: blur(10px) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-radius: 12px !important;
    }

    .mat-mdc-option {
      color: rgba(255, 255, 255, 0.9) !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
      }

      &.mdc-list-item--selected {
        background: rgba(33, 150, 243, 0.2) !important;
      }
    }

    // Button styling
    .mat-mdc-button, .mat-mdc-icon-button {
      border-radius: 12px !important;
      transition: all 200ms ease !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
      }
    }

    // Slide toggle styling
    .mat-mdc-slide-toggle {
      .mdc-switch__track {
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 12px !important;
      }

      &.mat-checked .mdc-switch__track {
        background: rgba(171, 71, 188, 0.5) !important;
      }
    }
  }
}

// Main Dashboard Layout
.dashboard-panels {
  display: flex;
  height: 100vh;
  width: 100%;
  gap: 1px; // Thin separator between panels
  background: rgba(255, 255, 255, 0.05); // Separator color
}

// Left Panel (25%) - Enhanced UI
.left-panel {
  width: 25%;
  min-width: 320px;
  background: rgba(0, 0, 0, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // Enhanced input styling for left panel
  ::ng-deep {
    .mat-mdc-form-field {
      width: 100%;
      margin-bottom: 0.8rem;

      .mat-mdc-text-field-wrapper {
        border-radius: 12px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        backdrop-filter: blur(10px);
        transition: all 200ms ease !important;
      }

      .mdc-text-field--outlined .mdc-notched-outline {
        border-color: rgba(255, 255, 255, 0.15) !important;
        border-radius: 12px !important;
      }

      .mdc-text-field--focused .mdc-notched-outline {
        border-color: rgba(33, 150, 243, 0.3) !important;
        box-shadow: 0 0 8px rgba(33, 150, 243, 0.2) !important;
      }

      input, textarea, .mat-mdc-select-value {
        color: #ffffff !important;
        padding: 1rem !important;
        font-size: 0.9rem !important;
      }

      .mat-mdc-form-field-infix {
        padding: 1rem !important;
        min-height: auto !important;
      }

      .mat-mdc-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      .mat-mdc-form-field-icon-suffix {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }

    .mat-mdc-select-panel {
      background: rgba(0, 0, 0, 0.95) !important;
      backdrop-filter: blur(10px) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-radius: 12px !important;
    }

    .mat-mdc-option {
      color: rgba(255, 255, 255, 0.9) !important;
      min-height: 44px !important;
      border-radius: 8px !important;
      margin: 0.2rem !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
      }

      &.mdc-list-item--selected {
        background: rgba(33, 150, 243, 0.2) !important;
      }
    }

    // Button styling
    .mat-mdc-button, .mat-mdc-icon-button {
      border-radius: 12px !important;
      transition: all 200ms ease !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        transform: translateY(-1px);
      }
    }
  }
}

// Center Panel (50%)
.center-panel {
  width: 50%;
  background: rgba(0, 0, 0, 0.98);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Right Panel (25%) - Enhanced Scrollable UI
.right-panel {
  width: 25%;
  min-width: 320px;
  background: rgba(0, 0, 0, 0.95);
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;

  // Custom scrollbar for right panel
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  // Enhanced styling for all components in right panel
  ::ng-deep {
    .mat-mdc-form-field {
      width: 100%;
      margin-bottom: 0.8rem;

      .mat-mdc-text-field-wrapper {
        border-radius: 12px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        backdrop-filter: blur(10px);
        transition: all 200ms ease !important;
      }

      .mdc-text-field--outlined .mdc-notched-outline {
        border-color: rgba(255, 255, 255, 0.15) !important;
        border-radius: 12px !important;
      }

      .mdc-text-field--focused .mdc-notched-outline {
        border-color: rgba(33, 150, 243, 0.3) !important;
        box-shadow: 0 0 8px rgba(33, 150, 243, 0.2) !important;
      }

      input, textarea, .mat-mdc-select-value {
        color: #ffffff !important;
        padding: 1rem !important;
        font-size: 0.9rem !important;
      }

      .mat-mdc-form-field-infix {
        padding: 1rem !important;
        min-height: auto !important;
      }

      .mat-mdc-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      .mat-mdc-form-field-icon-suffix {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }

    // Button styling
    .mat-mdc-button, .mat-mdc-icon-button {
      border-radius: 12px !important;
      transition: all 200ms ease !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        transform: translateY(-1px);
      }
    }

    // Card and section styling
    .section, .card, .analytics-card {
      background: rgba(255, 255, 255, 0.02) !important;
      border: 1px solid rgba(255, 255, 255, 0.05) !important;
      border-radius: 12px !important;
      backdrop-filter: blur(10px) !important;
      margin-bottom: 0.8rem !important;
      transition: all 200ms ease !important;

      &:hover {
        background: rgba(255, 255, 255, 0.03) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        transform: translateY(-1px);
      }
    }

    // List items styling
    .list-item, .suggestion-item, .analytics-item {
      background: rgba(255, 255, 255, 0.02) !important;
      border: 1px solid rgba(255, 255, 255, 0.05) !important;
      border-radius: 8px !important;
      padding: 0.8rem 1rem !important;
      margin-bottom: 0.8rem !important;
      backdrop-filter: blur(10px) !important;
      transition: all 200ms ease !important;
      min-height: 44px !important;

      &:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        transform: translateY(-1px);
      }
    }
  }
}

// Search Section
.search-section {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(255, 255, 255, 0.02);

  .search-container {
    margin-bottom: 0.8rem;

    .search-field {
      width: 100%;

      ::ng-deep {
        .mat-mdc-form-field-outline {
          color: rgba(255, 255, 255, 0.2);
        }

        .mat-mdc-form-field-focus-overlay {
          background: rgba(255, 255, 255, 0.05);
        }

        input {
          color: #ffffff;
          caret-color: #ffffff;
        }

        .mat-mdc-form-field-label {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .quick-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;

    .filter-select {
      flex: 1;
      font-size: 0.85rem;

      ::ng-deep {
        .mat-mdc-form-field-outline {
          color: rgba(255, 255, 255, 0.15);
        }

        .mdc-text-field--outlined .mdc-notched-outline__leading,
        .mdc-text-field--outlined .mdc-notched-outline__notch,
        .mdc-text-field--outlined .mdc-notched-outline__trailing {
          border-color: rgba(255, 255, 255, 0.15);
        }
      }
    }

    .sort-direction-btn {
      min-width: 44px;
      min-height: 44px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

// Legacy section title styling (now handled in section-header)
.section-title:not(.analytics-section .section-title):not(.ai-assistant-section .section-title) {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  transition: all 200ms ease;

  &:hover {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  mat-icon {
    font-size: 1.1rem;
    width: 1.1rem;
    height: 1.1rem;
    color: rgba(255, 255, 255, 0.6);
  }

  .conversation-count {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
  }
}

// KPI Section
.kpi-section {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(255, 255, 255, 0.01);
}

// Chat List Section
.chat-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0.5rem 1rem;

    .refresh-btn {
      min-width: 36px;
      min-height: 36px;
      color: rgba(255, 255, 255, 0.6);
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }
}

// Provider Tabs
.provider-tabs-container {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);

  .provider-tabs {
    ::ng-deep {
      .mat-mdc-tab-group {
        background: transparent;
      }

      .mat-mdc-tab-header {
        border-bottom: none;
        background: transparent;
      }

      .mat-mdc-tab-label-container {
        background: transparent;
      }

      .mat-mdc-tab {
        min-width: 120px;
        opacity: 0.7;
        transition: all 0.2s ease;

        &.mdc-tab--active {
          opacity: 1;
        }

        &:hover:not(.mdc-tab--active) {
          opacity: 0.9;
          background: rgba(255, 255, 255, 0.03);
        }
      }

      .mat-mdc-tab-ink-bar {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
        height: 2px;
      }
    }

    .tab-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0;

      mat-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
        transition: all 0.2s ease;

        &.connected {
          opacity: 1;
        }

        &.disconnected {
          opacity: 0.4;
        }
      }

      .tab-text {
        font-size: 0.85rem;
        font-weight: 500;
      }

      .connection-indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &.connected {
          background: #4caf50;
          box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
        }

        &.disconnected {
          background: #f44336;
          box-shadow: 0 0 4px rgba(244, 67, 54, 0.3);
        }
      }

      .unread-badge {
        ::ng-deep .mat-badge-content {
          background: #ff4444;
          color: #ffffff;
          font-size: 0.7rem;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }
}

// Chat Interface Container
.chat-interface-container {
  flex: 1;
  overflow: hidden;
}

// Analytics and AI Sections - Enhanced Scrollable with Collapse
.analytics-section,
.ai-assistant-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 0.8rem;
  backdrop-filter: blur(10px);
  transition: all 200ms ease;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.1);
  }

  &.collapsed {
    .section-content {
      max-height: 0;
      opacity: 0;
      padding: 0 1rem;
      overflow: hidden;
    }

    .collapse-btn mat-icon {
      transform: rotate(0deg);
    }
  }

  &:not(.collapsed) {
    .section-content {
      max-height: 1000px;
      opacity: 1;
      padding: 1rem;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .collapse-btn mat-icon {
      transform: rotate(180deg);
    }
  }

  // Section Header
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    cursor: pointer;
    transition: all 200ms ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    &:hover {
      background: rgba(255, 255, 255, 0.02);
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      font-size: 1rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      padding: 0;
      background: none;
      border: none;
      border-radius: 0;

      mat-icon {
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
        color: #ab47bc;
      }
    }

    .collapse-btn {
      min-width: 44px;
      min-height: 44px;
      color: rgba(255, 255, 255, 0.6);
      transition: all 200ms ease;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      mat-icon {
        transition: transform 200ms ease;
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }
    }
  }

  // Section Content
  .section-content {
    transition: all 300ms ease;

    // Custom scrollbar for sections
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.25);
      }
    }

    &.hidden {
      display: none;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  // Smooth scrolling
  scroll-behavior: smooth;

  // Enhanced visual states
  &:not(.collapsed) .section-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  &.collapsed .section-header {
    border-bottom: none;
  }

  // Accessibility improvements
  .section-header {
    &:focus-within {
      outline: 2px solid rgba(33, 150, 243, 0.5);
      outline-offset: 2px;
    }

    .collapse-btn {
      &:focus {
        outline: 2px solid rgba(33, 150, 243, 0.5);
        outline-offset: 2px;
      }
    }
  }
}

// Mobile Responsive Design
@media (max-width: 1200px) {
  .dashboard-panels {
    .left-panel,
    .right-panel {
      min-width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-panels {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .left-panel,
  .center-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  .left-panel {
    order: 1;
    max-height: 40vh;
  }

  .center-panel {
    order: 2;
    min-height: 50vh;
  }

  .right-panel {
    order: 3;
    max-height: 40vh;
  }

  // Ensure touch targets are 44px minimum
  .sort-direction-btn,
  .refresh-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .provider-tabs {
    ::ng-deep .mat-mdc-tab {
      min-width: 80px;
      min-height: 44px;
    }
  }
}

// Mobile Overlay for detailed views
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .mobile-panel {
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    overflow: auto;
  }
}

// Performance optimizations
.conversations-dashboard,
.dashboard-panels,
.left-panel,
.center-panel,
.right-panel {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Mobile responsive enhancements for collapsible sections
@media (max-width: 768px) {
  .analytics-section,
  .ai-assistant-section {
    .section-header {
      padding: 0.8rem;

      .section-title {
        font-size: 0.9rem;
        gap: 0.6rem;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }

      .collapse-btn {
        min-width: 40px;
        min-height: 40px;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }

    .section-content {
      padding: 0.8rem;
    }

    &.collapsed .section-content {
      padding: 0 0.8rem;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .provider-tabs,
  .sort-direction-btn,
  .refresh-btn,
  .section-content,
  .collapse-btn mat-icon {
    animation: none !important;
    transition: none !important;
  }
}
