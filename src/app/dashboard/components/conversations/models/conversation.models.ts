// Conversations Dashboard Data Models and Interfaces

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  status: 'active' | 'pending' | 'resolved' | 'escalated';
  satisfactionScore: number; // 1-5 stars
  totalInteractions: number;
  lastInteraction: Date;
  tags: string[];
  preferences: {
    language: string;
    timezone: string;
    communicationChannel: string;
  };
  purchaseHistory: PurchaseItem[];
  ticketHistory: TicketSummary[];
}

export interface PurchaseItem {
  id: string;
  productName: string;
  amount: number;
  currency: string;
  date: Date;
  status: 'completed' | 'pending' | 'refunded';
}

export interface TicketSummary {
  id: string;
  subject: string;
  status: 'open' | 'closed' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdDate: Date;
  resolvedDate?: Date;
  resolutionTime?: number; // in minutes
  satisfactionRating?: number;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'customer' | 'agent' | 'system' | 'ai';
  content: string;
  messageType: 'text' | 'image' | 'file' | 'audio' | 'video' | 'system';
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  metadata: {
    platform: CommunicationProvider;
    threadId?: string;
    replyToId?: string;
    attachments?: MessageAttachment[];
    reactions?: MessageReaction[];
    isEdited?: boolean;
    editedAt?: Date;
  };
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    confidence: number;
    intent: string;
    entities: string[];
    urgency: 'low' | 'medium' | 'high';
  };
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: Date;
}

export interface Conversation {
  id: string;
  customerId: string;
  customer: Customer;
  subject: string;
  status: 'active' | 'pending' | 'resolved' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedAgentId?: string;
  assignedAgentName?: string;
  platform: CommunicationProvider;
  createdAt: Date;
  updatedAt: Date;
  lastMessageAt: Date;
  messageCount: number;
  unreadCount: number;
  tags: string[];
  metadata: {
    source: string;
    campaignId?: string;
    referrer?: string;
    userAgent?: string;
    ipAddress?: string;
  };
  aiSummary?: {
    summary: string;
    keyPoints: string[];
    suggestedActions: string[];
    escalationRisk: number; // 0-1
  };
}

export type CommunicationProvider = 
  | 'email' 
  | 'whatsapp' 
  | 'facebook' 
  | 'instagram' 
  | 'tiktok' 
  | 'discord' 
  | 'slack' 
  | 'templates';

export interface ProviderConfig {
  id: CommunicationProvider;
  name: string;
  icon: string;
  color: string;
  isConnected: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'error' | 'pending';
  lastSync?: Date;
  features: ProviderFeature[];
  settings: Record<string, any>;
}

export interface ProviderFeature {
  id: string;
  name: string;
  enabled: boolean;
  description: string;
}

export interface AIInsight {
  id: string;
  type: 'suggestion' | 'translation' | 'sentiment' | 'escalation' | 'knowledge' | 'template';
  title: string;
  content: string;
  confidence: number; // 0-1
  timestamp: Date;
  conversationId: string;
  metadata: Record<string, any>;
  actions?: AIAction[];
}

export interface AIAction {
  id: string;
  label: string;
  type: 'apply_template' | 'translate' | 'escalate' | 'suggest_response' | 'search_kb';
  data: Record<string, any>;
}

export interface CustomerKPI {
  id: string;
  label: string;
  value: string | number;
  icon: string;
  change: number; // percentage change
  trend: 'up' | 'down' | 'stable';
  period: string;
  color: 'success' | 'warning' | 'error' | 'info';
}

export interface ConversationFilter {
  status?: string[];
  priority?: string[];
  platform?: CommunicationProvider[];
  assignedAgent?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
  searchQuery?: string;
}

export interface ConversationSort {
  field: 'lastMessageAt' | 'createdAt' | 'priority' | 'unreadCount' | 'customerName';
  direction: 'asc' | 'desc';
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userName: string;
  timestamp: Date;
}

export interface MessageTemplate {
  id: string;
  name: string;
  category: string;
  content: string;
  variables: string[];
  platform: CommunicationProvider[];
  tags: string[];
  usageCount: number;
  lastUsed?: Date;
}

export interface InteractionHistoryItem {
  id: string;
  type: 'message' | 'call' | 'meeting' | 'ticket' | 'purchase' | 'support';
  title: string;
  description: string;
  timestamp: Date;
  platform: string;
  status: string;
  metadata: Record<string, any>;
}

export interface SentimentAnalysis {
  overall: 'positive' | 'neutral' | 'negative';
  confidence: number;
  trend: Array<{
    timestamp: Date;
    sentiment: 'positive' | 'neutral' | 'negative';
    score: number;
  }>;
  keywords: Array<{
    word: string;
    sentiment: 'positive' | 'neutral' | 'negative';
    frequency: number;
  }>;
}

export interface ConversationMetrics {
  totalConversations: number;
  activeConversations: number;
  averageResponseTime: number; // in minutes
  customerSatisfactionAverage: number;
  resolutionRate: number; // percentage
  escalationRate: number; // percentage
  messageVolume: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}
