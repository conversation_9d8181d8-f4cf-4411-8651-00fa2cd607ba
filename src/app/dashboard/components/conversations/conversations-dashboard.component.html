<!-- Conversations Dashboard - Three Panel Layout -->
<div class="conversations-dashboard">
  <!-- Main Dashboard Layout -->
  <div class="dashboard-panels">
    <!-- Left Panel - Chat List & Customer KPIs (25%) -->
    <div class="left-panel">




      <!-- Chat List -->
      <div class="chat-list-section">
        <div class="section-header">
          <h3 class="section-title">
            <mat-icon>chat</mat-icon>
            Conversations
            <span class="conversation-count" *ngIf="filteredConversations.length">
              ({{ filteredConversations.length }})
            </span>
          </h3>
          <button 
            mat-icon-button 
            class="refresh-btn"
            (click)="loadConversations()"
            aria-label="Refresh conversations"
          >
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
        
        <app-chat-list
          [conversations]="filteredConversations"
          [selectedConversation]="selectedConversation"
          (conversationSelected)="onConversationSelected($event)"
          (filtersChanged)="onFiltersChanged($event)"
        >
        </app-chat-list>
      </div>
    </div>

    <!-- Center Panel - Main Chat Interface (50%) -->
    <div class="center-panel">
      <!-- Provider Tabs -->
      <div class="provider-tabs-container">
        <mat-tab-group
          [selectedIndex]="getProviderIndex(activeProvider)"
          (selectedTabChange)="onProviderChanged(providers[$event.index].id)"
          class="provider-tabs"
          animationDuration="200ms"
        >
          <mat-tab 
            *ngFor="let provider of providers" 
            [label]="provider.name"
            [disabled]="!provider.isConnected && provider.id !== 'templates'"
          >
            <ng-template mat-tab-label>
              <div class="tab-label">
                <mat-icon 
                  [style.color]="provider.color"
                  [class.connected]="provider.isConnected"
                  [class.disconnected]="!provider.isConnected"
                >
                  {{ provider.icon }}
                </mat-icon>
                <span class="tab-text">{{ provider.name }}</span>
                <div 
                  class="connection-indicator"
                  [class.connected]="provider.isConnected"
                  [class.disconnected]="!provider.isConnected"
                  [title]="provider.connectionStatus"
                ></div>
                <span 
                  class="unread-badge" 
                  *ngIf="getUnreadCountForProvider(provider.id) > 0"
                  [matBadge]="getUnreadCountForProvider(provider.id)"
                  matBadgeSize="small"
                  matBadgeColor="accent"
                ></span>
              </div>
            </ng-template>
          </mat-tab>
        </mat-tab-group>
      </div>

      <!-- Main Chat Interface -->
      <div class="chat-interface-container">
        <app-chat-interface
          [selectedConversation]="selectedConversation"
          [activeProvider]="activeProvider"
          [providerConfig]="getActiveProvider()"
          (messageSelected)="onMessageSelected($event)"
          (providerChanged)="onProviderChanged($event)">
        </app-chat-interface>
      </div>
    </div>

    <!-- Right Panel - AI Features & Analytics (25%) -->
    <div class="right-panel">
      <!-- Customer Analytics Section -->
      <div class="analytics-section" [class.collapsed]="customerProfileCollapsed">
        <div class="section-header" (click)="toggleCustomerProfile()">
          <h3 class="section-title">
            <mat-icon>person</mat-icon>
            Customer Profile
          </h3>
          <button mat-icon-button class="collapse-btn" [attr.aria-label]="customerProfileCollapsed ? 'Expand Customer Profile' : 'Collapse Customer Profile'">
            <mat-icon>{{ customerProfileCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
          </button>
        </div>
        <div class="section-content" [class.hidden]="customerProfileCollapsed">
          <app-customer-analytics
            [customer]="selectedConversation?.customer || null"
            [conversation]="selectedConversation">
          </app-customer-analytics>
        </div>
      </div>

      <!-- AI Assistant Section -->
      <div class="ai-assistant-section" [class.collapsed]="aiAssistantCollapsed">
        <div class="section-header" (click)="toggleAiAssistant()">
          <h3 class="section-title">
            <mat-icon>psychology</mat-icon>
            AI Assistant
          </h3>
          <button mat-icon-button class="collapse-btn" [attr.aria-label]="aiAssistantCollapsed ? 'Expand AI Assistant' : 'Collapse AI Assistant'">
            <mat-icon>{{ aiAssistantCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
          </button>
        </div>
        <div class="section-content" [class.hidden]="aiAssistantCollapsed">
          <app-ai-assistant
            [conversation]="selectedConversation"
            [activeProvider]="activeProvider"
            (autoReplyGenerated)="onAutoReplyGenerated($event)"
            (replySelected)="onReplySelected($event)">
          </app-ai-assistant>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Responsive Overlay -->
  <div class="mobile-overlay" *ngIf="showMobileOverlay" (click)="closeMobileOverlay()">
    <div class="mobile-panel" (click)="$event.stopPropagation()">
      <!-- Mobile panel content will be dynamically loaded -->
    </div>
  </div>
</div>
