import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatBadgeModule } from '@angular/material/badge';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';

// Import models
import {
  Conversation,
  CommunicationProvider,
  ProviderConfig,
  CustomerKPI,
  ConversationFilter,
  ConversationSort,
  AIInsight,
  ConversationMetrics
} from './models/conversation.models';

// Import child components (to be created)
import { ChatListComponent } from './components/chat-list/chat-list.component';

import { ChatInterfaceComponent } from './components/chat-interface/chat-interface.component';
import { CustomerAnalyticsComponent } from './components/customer-analytics/customer-analytics.component';
import { AiAssistantComponent } from './components/ai-assistant/ai-assistant.component';

@Component({
  selector: 'app-conversations-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatTabsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatBadgeModule,
    FormsModule,
    ChatListComponent,
    ChatInterfaceComponent,
    CustomerAnalyticsComponent,
    AiAssistantComponent
  ],
  templateUrl: './conversations-dashboard.component.html',
  styleUrls: ['./conversations-dashboard.component.scss']
})
export class ConversationsDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // State management
  selectedConversation: Conversation | null = null;
  activeProvider: CommunicationProvider = 'email';
  conversations: Conversation[] = [];
  filteredConversations: Conversation[] = [];
  customerKpis: CustomerKPI[] = [];
  aiInsights: AIInsight[] = [];
  metrics: ConversationMetrics | null = null;

  // UI state
  searchQuery = '';
  selectedFilters: ConversationFilter = {};
  sortConfig: ConversationSort = { field: 'lastMessageAt', direction: 'desc' };
  sidebarCollapsed = false;

  // Right panel collapse states
  customerProfileCollapsed = false;
  aiAssistantCollapsed = false;

  // Communication providers configuration
  providers: ProviderConfig[] = [
    {
      id: 'email',
      name: 'Email',
      icon: 'email',
      color: '#4285f4',
      isConnected: true,
      connectionStatus: 'connected',
      lastSync: new Date(),
      features: [
        { id: 'threading', name: 'Email Threading', enabled: true, description: 'Group related emails' },
        { id: 'autoresponder', name: 'Auto Responder', enabled: true, description: 'Automatic responses' }
      ],
      settings: {}
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: 'chat',
      color: '#25d366',
      isConnected: true,
      connectionStatus: 'connected',
      lastSync: new Date(),
      features: [
        { id: 'read_receipts', name: 'Read Receipts', enabled: true, description: 'Message read status' },
        { id: 'media_sharing', name: 'Media Sharing', enabled: true, description: 'Share images and files' }
      ],
      settings: {}
    },
    {
      id: 'facebook',
      name: 'Facebook Messenger',
      icon: 'facebook',
      color: '#1877f2',
      isConnected: false,
      connectionStatus: 'disconnected',
      features: [
        { id: 'reactions', name: 'Message Reactions', enabled: true, description: 'React to messages' },
        { id: 'stories', name: 'Story Replies', enabled: false, description: 'Respond to story mentions' }
      ],
      settings: {}
    },
    {
      id: 'instagram',
      name: 'Instagram Direct',
      icon: 'camera_alt',
      color: '#e4405f',
      isConnected: false,
      connectionStatus: 'disconnected',
      features: [
        { id: 'media_rich', name: 'Rich Media', enabled: true, description: 'Images and videos' },
        { id: 'story_replies', name: 'Story Replies', enabled: true, description: 'Respond to story mentions' }
      ],
      settings: {}
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      icon: 'music_note',
      color: '#ff0050',
      isConnected: false,
      connectionStatus: 'disconnected',
      features: [
        { id: 'video_responses', name: 'Video Responses', enabled: false, description: 'Respond with videos' },
        { id: 'comment_management', name: 'Comment Management', enabled: true, description: 'Manage video comments' }
      ],
      settings: {}
    },
    {
      id: 'discord',
      name: 'Discord',
      icon: 'forum',
      color: '#5865f2',
      isConnected: false,
      connectionStatus: 'disconnected',
      features: [
        { id: 'server_management', name: 'Server Management', enabled: true, description: 'Manage Discord servers' },
        { id: 'voice_support', name: 'Voice Support', enabled: false, description: 'Voice channel support' }
      ],
      settings: {}
    },
    {
      id: 'slack',
      name: 'Slack',
      icon: 'work',
      color: '#4a154b',
      isConnected: true,
      connectionStatus: 'connected',
      lastSync: new Date(),
      features: [
        { id: 'channels', name: 'Channel Support', enabled: true, description: 'Multi-channel support' },
        { id: 'threads', name: 'Thread Management', enabled: true, description: 'Threaded conversations' }
      ],
      settings: {}
    },
    {
      id: 'templates',
      name: 'Templates',
      icon: 'description',
      color: '#6c757d',
      isConnected: true,
      connectionStatus: 'connected',
      features: [
        { id: 'quick_replies', name: 'Quick Replies', enabled: true, description: 'Pre-defined responses' },
        { id: 'variables', name: 'Template Variables', enabled: true, description: 'Dynamic content insertion' }
      ],
      settings: {}
    }
  ];

  constructor() {}

  ngOnInit(): void {
    this.initializeData();
    this.loadConversations();
    this.loadCustomerKpis();
    this.loadMetrics();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeData(): void {
    // Initialize with mock data for demonstration
    this.generateMockData();
  }

  loadConversations(): void {
    // Load conversations immediately
    this.applyFiltersAndSort();
  }

  private loadCustomerKpis(): void {
    // Load customer KPIs
    this.customerKpis = [
      {
        id: 'active_conversations',
        label: 'Active Conversations',
        value: 24,
        icon: 'chat',
        change: 12.5,
        trend: 'up',
        period: 'vs last week',
        color: 'success'
      },
      {
        id: 'avg_response_time',
        label: 'Avg Response Time',
        value: '2.3 min',
        icon: 'schedule',
        change: -8.2,
        trend: 'down',
        period: 'vs last week',
        color: 'success'
      },
      {
        id: 'satisfaction_score',
        label: 'Satisfaction Score',
        value: '4.8/5',
        icon: 'star',
        change: 3.1,
        trend: 'up',
        period: 'vs last month',
        color: 'success'
      },
      {
        id: 'urgent_tickets',
        label: 'Urgent Tickets',
        value: 3,
        icon: 'priority_high',
        change: 50,
        trend: 'up',
        period: 'vs yesterday',
        color: 'warning'
      }
    ];
  }

  private loadMetrics(): void {
    this.metrics = {
      totalConversations: 156,
      activeConversations: 24,
      averageResponseTime: 2.3,
      customerSatisfactionAverage: 4.8,
      resolutionRate: 94.2,
      escalationRate: 5.8,
      messageVolume: {
        today: 89,
        thisWeek: 542,
        thisMonth: 2341
      }
    };
  }

  // Event handlers
  onConversationSelected(conversation: Conversation): void {
    this.selectedConversation = conversation;
  }

  onProviderChanged(provider: CommunicationProvider): void {
    this.activeProvider = provider;
    this.applyFiltersAndSort();
  }

  onSearchChanged(query: string): void {
    this.searchQuery = query;
    this.applyFiltersAndSort();
  }

  onFiltersChanged(filters: ConversationFilter): void {
    this.selectedFilters = filters;
    this.applyFiltersAndSort();
  }

  onSortChanged(sort: ConversationSort): void {
    this.sortConfig = sort;
    this.applyFiltersAndSort();
  }

  private applyFiltersAndSort(): void {
    let filtered = [...this.conversations];

    // Apply search filter
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(conv => 
        conv.customer.name.toLowerCase().includes(query) ||
        conv.subject.toLowerCase().includes(query) ||
        conv.customer.email.toLowerCase().includes(query)
      );
    }

    // Apply provider filter
    if (this.activeProvider !== 'templates') {
      filtered = filtered.filter(conv => conv.platform === this.activeProvider);
    }

    // Apply other filters
    if (this.selectedFilters.status?.length) {
      filtered = filtered.filter(conv => this.selectedFilters.status!.includes(conv.status));
    }

    if (this.selectedFilters.priority?.length) {
      filtered = filtered.filter(conv => this.selectedFilters.priority!.includes(conv.priority));
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (this.sortConfig.field === 'customerName') {
        aValue = a.customer.name;
        bValue = b.customer.name;
      } else {
        aValue = a[this.sortConfig.field as keyof Conversation];
        bValue = b[this.sortConfig.field as keyof Conversation];
      }

      if (aValue < bValue) return this.sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return this.sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });

    this.filteredConversations = filtered;
  }

  private generateMockData(): void {
    // Generate realistic mock conversations for demonstration
    const mockCustomers = [
      { name: 'Sarah Johnson', email: '<EMAIL>', avatar: '', phone: '******-0123' },
      { name: 'Michael Chen', email: '<EMAIL>', avatar: '', phone: '******-0124' },
      { name: 'Emma Rodriguez', email: '<EMAIL>', avatar: '', phone: '******-0125' },
      { name: 'David Kim', email: '<EMAIL>', avatar: '', phone: '******-0126' },
      { name: 'Lisa Thompson', email: '<EMAIL>', avatar: '', phone: '******-0127' },
      { name: 'Alex Petrov', email: '<EMAIL>', avatar: '', phone: '******-0128' },
      { name: 'Maria Garcia', email: '<EMAIL>', avatar: '', phone: '******-0129' },
      { name: 'James Wilson', email: '<EMAIL>', avatar: '', phone: '******-0130' }
    ];

    const mockMessages = [
      'Hi, I need help with my recent order #ORD-12345. It seems to be delayed and I haven\'t received any tracking information.',
      'The payment failed but I was still charged $299. Can you help me resolve this billing issue?',
      'I love your product! Can you tell me about the premium features and enterprise pricing?',
      'I\'m having trouble accessing my account. The password reset email isn\'t working properly.',
      'When will the new AI features be available? I\'m really excited about the automation capabilities!',
      'I need to update my billing information and add a new team member. How can I do that?',
      'The file upload feature isn\'t working properly. Getting error 500 when uploading documents over 10MB.',
      'Thank you for the quick response! The issue has been resolved and everything is working perfectly now.',
      'Can I schedule a demo for my team? We\'re interested in the enterprise plan for 50+ users.',
      'I accidentally deleted important project data. Is there a way to recover it from backups?'
    ];

    const platforms: CommunicationProvider[] = ['email', 'whatsapp', 'facebook', 'instagram', 'discord'];
    const statuses = ['active', 'pending', 'resolved', 'escalated'];
    const priorities = ['low', 'medium', 'high', 'urgent'];

    this.conversations = mockCustomers.map((customer, index) => ({
      id: `conv-${index + 1}`,
      customerId: `cust-${index + 1}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      messageCount: Math.floor(Math.random() * 20) + 1,
      customer: {
        id: `cust-${index + 1}`,
        name: customer.name,
        email: customer.email,
        avatar: customer.avatar,
        phone: customer.phone,
        tags: index < 2 ? ['VIP', 'Premium'] : index < 4 ? ['Premium'] : ['Standard'],
        totalInteractions: Math.floor(Math.random() * 50) + 5,
        satisfactionScore: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0 - 5.0
        lastInteraction: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        status: 'active',
        preferences: {
          language: 'en',
          timezone: 'UTC',
          communicationChannel: platforms[Math.floor(Math.random() * platforms.length)]
        },
        purchaseHistory: [
          {
            id: `purchase-${index + 1}`,
            productName: index % 2 === 0 ? 'Premium Plan' : 'Enterprise Suite',
            amount: index % 2 === 0 ? 99.99 : 299.99,
            currency: 'USD',
            date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
            status: 'completed'
          }
        ],
        ticketHistory: [
          {
            id: `ticket-${index + 1}`,
            subject: `Support Request #${1000 + index}`,
            status: 'resolved',
            priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
            createdDate: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000),
            resolvedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
          }
        ]
      },
      subject: index === 0 ? 'Order Delivery Issue' :
               index === 1 ? 'Payment Processing Error' :
               index === 2 ? 'Product Feature Inquiry' :
               index === 3 ? 'Account Access Problem' :
               index === 4 ? 'Feature Request Discussion' :
               index === 5 ? 'Billing Update Request' :
               index === 6 ? 'Technical Bug Report' :
               'General Support Question',
      lastMessage: mockMessages[index],
      lastMessageAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      status: statuses[Math.floor(Math.random() * statuses.length)] as any,
      priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
      unreadCount: Math.floor(Math.random() * 5),
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      assignedAgent: `agent-${Math.floor(Math.random() * 3) + 1}`,
      tags: index < 2 ? ['urgent', 'order'] :
            index < 4 ? ['billing', 'payment'] :
            index < 6 ? ['feature', 'inquiry'] : ['support', 'general'],
      metadata: {
        source: platforms[Math.floor(Math.random() * platforms.length)],
        campaignId: Math.random() > 0.5 ? `campaign-${index + 1}` : undefined,
        referrer: Math.random() > 0.5 ? 'https://example.com' : undefined,
        userAgent: 'Mozilla/5.0 (compatible)',
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`
      }
    }));

    // Generate mock KPIs
    this.customerKpis = [
      {
        id: 'response_time',
        label: 'Avg Response Time',
        value: '2.3 min',
        change: -15,
        trend: 'down',
        icon: 'schedule',
        color: 'success',
        period: 'Last 24h'
      },
      {
        id: 'satisfaction',
        label: 'Customer Satisfaction',
        value: '4.8/5',
        change: 8,
        trend: 'up',
        icon: 'sentiment_very_satisfied',
        color: 'info',
        period: 'This week'
      },
      {
        id: 'resolution_rate',
        label: 'Resolution Rate',
        value: '94%',
        change: 3,
        trend: 'up',
        icon: 'check_circle',
        color: 'warning',
        period: 'This month'
      },
      {
        id: 'urgent_tickets',
        label: 'Urgent Tickets',
        value: '3',
        change: -40,
        trend: 'down',
        icon: 'priority_high',
        color: 'error',
        period: 'Today'
      }
    ];
  }

  // Utility methods
  getActiveProvider(): ProviderConfig {
    return this.providers.find(p => p.id === this.activeProvider) || this.providers[0];
  }

  getConnectedProvidersCount(): number {
    return this.providers.filter(p => p.isConnected).length;
  }

  getTotalUnreadCount(): number {
    return this.filteredConversations.reduce((sum, conv) => sum + conv.unreadCount, 0);
  }

  // Additional methods for template
  getProviderIndex(providerId: CommunicationProvider): number {
    return this.providers.findIndex(p => p.id === providerId);
  }

  getUnreadCountForProvider(providerId: CommunicationProvider): number {
    return this.filteredConversations
      .filter(conv => conv.platform === providerId)
      .reduce((sum, conv) => sum + conv.unreadCount, 0);
  }

  onMessageSelected(message: any): void {
    console.log('Message selected:', message);
  }

  onInsightApplied(insight: AIInsight): void {
    console.log('AI insight applied:', insight);
  }

  onEscalationRequested(conversation: Conversation): void {
    console.log('Escalation requested for conversation:', conversation.id);
  }

  // Mobile overlay methods
  showMobileOverlay = false;

  closeMobileOverlay(): void {
    this.showMobileOverlay = false;
  }

  onAutoReplyGenerated(reply: string): void {
    console.log('Auto-reply generated:', reply);
  }

  onReplySelected(reply: string): void {
    console.log('Reply selected:', reply);
  }

  // Right panel collapse methods
  toggleCustomerProfile(): void {
    this.customerProfileCollapsed = !this.customerProfileCollapsed;
  }

  toggleAiAssistant(): void {
    this.aiAssistantCollapsed = !this.aiAssistantCollapsed;
  }
}
