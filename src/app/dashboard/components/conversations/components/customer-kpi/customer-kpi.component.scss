// Customer KPI Component - Futuristic Dark Theme
.kpi-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

// KPI Grid
.kpi-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.8rem;
}

// KPI Card
.kpi-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 0.8rem 1rem;
  margin-bottom: 0.8rem;
  transition: all 200ms ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  min-height: 44px;
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }

  &.animate {
    .kpi-value {
      animation: countUp 0.8s ease-out;
    }

    .progress-bar {
      animation: progressFill 1s ease-out 0.2s both;
    }
  }

  // Glow effect for urgent items
  &[data-kpi-id="urgent_tickets"] {
    border-color: rgba(244, 67, 54, 0.3);
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.1);
  }
}

// KPI Header
.kpi-header {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  margin-bottom: 0.8rem;

  .kpi-icon-container {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .kpi-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }

  .kpi-info {
    flex: 1;
    min-width: 0;

    .kpi-label {
      font-size: 0.85rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin: 0 0 0.2rem 0;
      line-height: 1.2;
    }

    .kpi-period {
      font-size: 0.7rem;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1;
    }
  }
}

// KPI Value Section
.kpi-value-section {
  margin-bottom: 0.8rem;

  .kpi-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
    line-height: 1;
  }

  .kpi-progress {
    .progress-bar {
      height: 4px;
      border-radius: 2px;
      background: rgba(255, 255, 255, 0.1);

      ::ng-deep {
        .mat-mdc-progress-bar-buffer {
          background: rgba(255, 255, 255, 0.05);
        }

        .mat-mdc-progress-bar-fill::after {
          background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
        }

        .mat-mdc-progress-bar-background {
          fill: rgba(255, 255, 255, 0.05);
        }
      }
    }
  }
}

// KPI Change Indicator
.kpi-change {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;

  .change-indicator {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 500;

    .trend-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }

    .change-value {
      font-weight: 600;
    }
  }
}

// KPI Actions
.kpi-actions {
  display: flex;
  justify-content: flex-end;
  opacity: 0;
  transition: opacity 0.2s ease;

  .kpi-action-btn {
    min-width: 28px;
    min-height: 28px;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;

    &:hover {
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
    }

    mat-icon {
      font-size: 0.9rem;
      width: 0.9rem;
      height: 0.9rem;
    }
  }
}

.kpi-card:hover .kpi-actions {
  opacity: 1;
}

// Metrics Summary
.metrics-summary {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1rem;
  margin-top: auto;

  .summary-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: rgba(255, 255, 255, 0.7);
    }

    .refresh-btn {
      margin-left: auto;
      min-width: 28px;
      min-height: 28px;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      mat-icon {
        font-size: 0.9rem;
        width: 0.9rem;
        height: 0.9rem;
      }
    }
  }

  .summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem;
    margin-bottom: 1rem;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.05);

      .stat-label {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 0.2rem;
      }

      .stat-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

// Volume Chart
.volume-chart {
  .chart-header {
    margin-bottom: 0.8rem;

    .chart-title {
      font-size: 0.8rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .volume-bars {
    display: flex;
    gap: 0.8rem;
    align-items: flex-end;
    height: 60px;

    .volume-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.3rem;

      .volume-bar {
        width: 100%;
        height: 40px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px 4px 0 0;
        position: relative;
        overflow: hidden;

        .volume-fill {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(180deg, #4caf50 0%, #2e7d32 100%);
          border-radius: 4px 4px 0 0;
          transition: height 0.8s ease-out;

          &.full {
            height: 100%;
          }
        }
      }

      .volume-label {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.6);
      }

      .volume-value {
        font-size: 0.75rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

// No KPIs State
.no-kpis {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  flex: 1;

  .no-kpis-icon {
    margin-bottom: 1rem;

    mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      opacity: 0.5;
    }
  }

  h4 {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    max-width: 200px;
  }

  .refresh-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
    }

    mat-icon {
      margin-right: 0.3rem;
      font-size: 0.9rem;
      width: 0.9rem;
      height: 0.9rem;
    }
  }
}

// Animations
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressFill {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

// Mobile Responsive Design
@media (max-width: 768px) {
  .kpi-card {
    padding: 0.8rem;
  }

  .kpi-header {
    gap: 0.6rem;

    .kpi-icon-container {
      width: 32px;
      height: 32px;

      .kpi-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }

    .kpi-info .kpi-label {
      font-size: 0.8rem;
    }
  }

  .kpi-value-section .kpi-value {
    font-size: 1.2rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;

    .stat-item {
      flex-direction: row;
      justify-content: space-between;
      text-align: left;
    }
  }

  .volume-bars {
    height: 50px;

    .volume-item .volume-bar {
      height: 30px;
    }
  }

  // Ensure touch targets are 44px minimum
  .kpi-action-btn,
  .refresh-btn {
    min-width: 44px;
    min-height: 44px;
  }
}

// Performance optimizations
.kpi-card,
.kpi-actions,
.volume-fill {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .kpi-card,
  .kpi-actions,
  .volume-fill {
    transition: none !important;
    animation: none !important;
  }

  @keyframes countUp {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes progressFill {
    from { transform: scaleX(0); }
    to { transform: scaleX(1); }
  }
}
