import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { 
  CustomerKPI, 
  ConversationMetrics 
} from '../../models/conversation.models';

@Component({
  selector: 'app-customer-kpi',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatProgressBarModule
  ],
  templateUrl: './customer-kpi.component.html',
  styleUrls: ['./customer-kpi.component.scss']
})
export class CustomerKpiComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  @Input() kpis: CustomerKPI[] = [];
  @Input() metrics: ConversationMetrics | null = null;

  // Animation state
  animateValues = false;

  constructor() {}

  ngOnInit(): void {
    // Trigger animation after component loads
    setTimeout(() => {
      this.animateValues = true;
    }, 100);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getKpiIcon(kpi: CustomerKPI): string {
    return kpi.icon;
  }

  getKpiColor(kpi: CustomerKPI): string {
    const colorMap = {
      success: '#4caf50',
      warning: '#ff9800',
      error: '#f44336',
      info: '#2196f3'
    };
    return colorMap[kpi.color] || '#9e9e9e';
  }

  getTrendIcon(trend: string): string {
    switch (trend) {
      case 'up': return 'trending_up';
      case 'down': return 'trending_down';
      case 'stable': return 'trending_flat';
      default: return 'remove';
    }
  }

  getTrendColor(trend: string, change: number): string {
    if (trend === 'stable') return '#9e9e9e';
    
    // For response time, down is good (green), up is bad (red)
    // For satisfaction and active conversations, up is good (green), down is bad (red)
    // For urgent tickets, up is bad (red), down is good (green)
    
    if (change > 0) {
      return trend === 'up' ? '#4caf50' : '#f44336';
    } else {
      return trend === 'down' ? '#4caf50' : '#f44336';
    }
  }

  formatChange(change: number): string {
    const absChange = Math.abs(change);
    return `${change > 0 ? '+' : ''}${absChange}%`;
  }

  getProgressValue(kpi: CustomerKPI): number {
    // Convert KPI values to progress percentages for visual representation
    switch (kpi.id) {
      case 'satisfaction_score':
        // Extract numeric value from "4.8/5" format
        const scoreMatch = kpi.value.toString().match(/(\d+\.?\d*)/);
        if (scoreMatch) {
          const score = parseFloat(scoreMatch[1]);
          return (score / 5) * 100;
        }
        return 0;
      
      case 'avg_response_time':
        // For response time, lower is better, so invert the scale
        // Assuming 5 minutes is the maximum acceptable time
        const timeMatch = kpi.value.toString().match(/(\d+\.?\d*)/);
        if (timeMatch) {
          const time = parseFloat(timeMatch[1]);
          return Math.max(0, 100 - (time / 5) * 100);
        }
        return 0;
      
      case 'active_conversations':
        // Assuming 50 is the maximum capacity
        const activeCount = typeof kpi.value === 'number' ? kpi.value : parseInt(kpi.value.toString());
        return Math.min(100, (activeCount / 50) * 100);
      
      case 'urgent_tickets':
        // For urgent tickets, lower is better
        const urgentCount = typeof kpi.value === 'number' ? kpi.value : parseInt(kpi.value.toString());
        return Math.max(0, 100 - (urgentCount / 10) * 100);
      
      default:
        return 75; // Default progress value
    }
  }

  getProgressColor(kpi: CustomerKPI): string {
    const progress = this.getProgressValue(kpi);
    
    if (progress >= 80) return '#4caf50'; // Green
    if (progress >= 60) return '#ff9800'; // Orange
    if (progress >= 40) return '#ff5722'; // Deep Orange
    return '#f44336'; // Red
  }

  refreshKpis(): void {
    // Emit refresh event or call service to reload KPIs
    console.log('Refreshing KPIs...');
    // This would typically call a service method
  }

  getMetricsSummary(): string {
    if (!this.metrics) return 'No metrics available';
    
    return `${this.metrics.totalConversations} total conversations, ${this.metrics.resolutionRate}% resolution rate`;
  }

  trackByKpiId(index: number, kpi: CustomerKPI): string {
    return kpi.id;
  }

  onKpiDetails(kpi: CustomerKPI): void {
    // Emit event or navigate to detailed view
    console.log('View KPI details:', kpi.id);
    // This would typically emit an event or open a modal with detailed metrics
  }
}
