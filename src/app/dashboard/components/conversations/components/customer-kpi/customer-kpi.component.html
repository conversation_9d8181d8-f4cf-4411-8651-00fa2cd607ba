<!-- Customer KPI Component -->
<div class="kpi-container">
  <!-- KPI Cards Grid -->
  <div class="kpi-grid" *ngIf="kpis.length > 0; else noKpis">
    <div 
      *ngFor="let kpi of kpis; trackBy: trackByKpiId"
      class="kpi-card"
      [class.animate]="animateValues"
      [attr.data-kpi-id]="kpi.id"
    >
      <!-- KPI Header -->
      <div class="kpi-header">
        <div class="kpi-icon-container">
          <mat-icon 
            class="kpi-icon"
            [style.color]="getKpiColor(kpi)"
          >
            {{ getKpiIcon(kpi) }}
          </mat-icon>
        </div>
        <div class="kpi-info">
          <h4 class="kpi-label">{{ kpi.label }}</h4>
          <div class="kpi-period">{{ kpi.period }}</div>
        </div>
      </div>

      <!-- KPI Value -->
      <div class="kpi-value-section">
        <div class="kpi-value">{{ kpi.value }}</div>
        
        <!-- Progress Bar for Visual Representation -->
        <div class="kpi-progress">
          <mat-progress-bar
            mode="determinate"
            [value]="getProgressValue(kpi)"
            [color]="'primary'"
            class="progress-bar"
          ></mat-progress-bar>
        </div>
      </div>

      <!-- KPI Change Indicator -->
      <div class="kpi-change">
        <div 
          class="change-indicator"
          [style.color]="getTrendColor(kpi.trend, kpi.change)"
        >
          <mat-icon class="trend-icon">{{ getTrendIcon(kpi.trend) }}</mat-icon>
          <span class="change-value">{{ formatChange(kpi.change) }}</span>
        </div>
      </div>

      <!-- KPI Actions -->
      <div class="kpi-actions">
        <button 
          mat-icon-button 
          class="kpi-action-btn"
          [matTooltip]="'View details for ' + kpi.label"
          (click)="onKpiDetails(kpi)"
        >
          <mat-icon>info</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Metrics Summary -->
  <div class="metrics-summary" *ngIf="metrics">
    <div class="summary-header">
      <mat-icon>analytics</mat-icon>
      <span>Quick Stats</span>
      <button 
        mat-icon-button 
        class="refresh-btn"
        (click)="refreshKpis()"
        matTooltip="Refresh metrics"
      >
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
    
    <div class="summary-stats">
      <div class="stat-item">
        <span class="stat-label">Total</span>
        <span class="stat-value">{{ metrics.totalConversations }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Active</span>
        <span class="stat-value">{{ metrics.activeConversations }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Resolution</span>
        <span class="stat-value">{{ metrics.resolutionRate }}%</span>
      </div>
    </div>

    <!-- Message Volume Chart -->
    <div class="volume-chart">
      <div class="chart-header">
        <span class="chart-title">Message Volume</span>
      </div>
      <div class="volume-bars">
        <div class="volume-item">
          <div class="volume-bar">
            <div 
              class="volume-fill"
              [style.height.%]="(metrics.messageVolume.today / metrics.messageVolume.thisMonth) * 100"
            ></div>
          </div>
          <span class="volume-label">Today</span>
          <span class="volume-value">{{ metrics.messageVolume.today }}</span>
        </div>
        <div class="volume-item">
          <div class="volume-bar">
            <div 
              class="volume-fill"
              [style.height.%]="(metrics.messageVolume.thisWeek / metrics.messageVolume.thisMonth) * 100"
            ></div>
          </div>
          <span class="volume-label">Week</span>
          <span class="volume-value">{{ metrics.messageVolume.thisWeek }}</span>
        </div>
        <div class="volume-item">
          <div class="volume-bar">
            <div class="volume-fill full"></div>
          </div>
          <span class="volume-label">Month</span>
          <span class="volume-value">{{ metrics.messageVolume.thisMonth }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- No KPIs State -->
  <ng-template #noKpis>
    <div class="no-kpis">
      <div class="no-kpis-icon">
        <mat-icon>analytics</mat-icon>
      </div>
      <h4>No KPIs Available</h4>
      <p>Performance metrics will appear here once data is available.</p>
      <button 
        mat-button 
        class="refresh-btn"
        (click)="refreshKpis()"
      >
        <mat-icon>refresh</mat-icon>
        Refresh
      </button>
    </div>
  </ng-template>
</div>
