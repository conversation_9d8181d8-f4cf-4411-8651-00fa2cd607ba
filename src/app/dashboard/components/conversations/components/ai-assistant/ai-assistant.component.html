<!-- AI Assistant Component - Auto-Reply Focus -->
<div class="ai-assistant">
  <!-- Loading State -->
  <div class="assistant-loading" *ngIf="isLoading">
    <div class="loading-content">
      <mat-icon class="spinning">psychology</mat-icon>
      <span>AI analyzing conversation...</span>
    </div>
  </div>

  <!-- No Conversation Selected -->
  <div class="no-conversation" *ngIf="!conversation && !isLoading">
    <div class="no-conversation-content">
      <mat-icon>psychology</mat-icon>
      <h4>AI Assistant Ready</h4>
      <p>Select a conversation to enable AI-powered auto-replies and suggestions.</p>
    </div>
  </div>

  <!-- AI Assistant Content -->
  <div class="assistant-content" *ngIf="conversation && !isLoading">
    <!-- Auto-Reply Settings Section -->
    <div class="assistant-section settings-section">
      <div class="section-header">
        <mat-icon class="section-icon">smart_toy</mat-icon>
        <h4 class="section-title">Auto-Reply Settings</h4>
      </div>

      <div class="section-content">
        <!-- Auto-Reply Toggle -->
        <div class="setting-item">
          <div class="setting-info">
            <span class="setting-label">Enable Auto-Reply</span>
            <span class="setting-description">Automatically respond to customer messages</span>
          </div>
          <mat-slide-toggle
            [(ngModel)]="autoReplyEnabled"
            (change)="onToggleAutoReply()"
            color="primary"
          ></mat-slide-toggle>
        </div>

        <!-- Auto-Reply Mode -->
        <div class="setting-item" *ngIf="autoReplyEnabled">
          <div class="setting-info">
            <span class="setting-label">Reply Mode</span>
            <span class="setting-description">How auto-replies are sent</span>
          </div>
          <mat-form-field appearance="outline" class="mode-select">
            <mat-select 
              [(value)]="autoReplyMode"
              (selectionChange)="onAutoReplyModeChange()"
            >
              <mat-option value="instant">Instant</mat-option>
              <mat-option value="delayed">Delayed</mat-option>
              <mat-option value="manual">Manual Approval</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Delay Setting -->
        <div class="setting-item" *ngIf="autoReplyEnabled && autoReplyMode === 'delayed'">
          <div class="setting-info">
            <span class="setting-label">Reply Delay</span>
            <span class="setting-description">Seconds before auto-reply is sent</span>
          </div>
          <mat-form-field appearance="outline" class="delay-input">
            <input 
              matInput 
              type="number" 
              [(ngModel)]="autoReplyDelay"
              (change)="onDelayChange()"
              min="5"
              max="300"
            />
            <span matSuffix>sec</span>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Real-time Analysis Section -->
    <div class="assistant-section analysis-section">
      <div class="section-header">
        <mat-icon class="section-icon">analytics</mat-icon>
        <h4 class="section-title">Real-time Analysis</h4>
      </div>

      <div class="section-content">
        <!-- Sentiment Analysis -->
        <div class="analysis-item">
          <div class="analysis-header">
            <mat-icon 
              class="analysis-icon"
              [style.color]="getSentimentColor()"
            >
              {{ getSentimentIcon() }}
            </mat-icon>
            <div class="analysis-info">
              <span class="analysis-label">Customer Sentiment</span>
              <span class="analysis-value">{{ currentSentiment | titlecase }}</span>
            </div>
          </div>
        </div>

        <!-- Urgency Level -->
        <div class="analysis-item">
          <div class="analysis-header">
            <mat-icon 
              class="analysis-icon"
              [style.color]="getUrgencyColor()"
            >
              {{ urgencyLevel === 'high' ? 'priority_high' : urgencyLevel === 'medium' ? 'flag' : 'low_priority' }}
            </mat-icon>
            <div class="analysis-info">
              <span class="analysis-label">Urgency Level</span>
              <span class="analysis-value">{{ urgencyLevel | titlecase }}</span>
            </div>
          </div>
        </div>

        <!-- Customer Intent -->
        <div class="analysis-item">
          <div class="analysis-header">
            <mat-icon class="analysis-icon">psychology</mat-icon>
            <div class="analysis-info">
              <span class="analysis-label">Detected Intent</span>
              <span class="analysis-value">{{ customerIntent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Suggested Replies Section -->
    <div class="assistant-section suggestions-section">
      <div class="section-header">
        <mat-icon class="section-icon">lightbulb</mat-icon>
        <h4 class="section-title">AI Suggested Replies</h4>
        <button 
          mat-icon-button 
          class="refresh-btn"
          (click)="onGenerateNewReplies()"
          [disabled]="isGeneratingReplies"
          matTooltip="Generate new suggestions"
        >
          <mat-icon [class.spinning]="isGeneratingReplies">refresh</mat-icon>
        </button>
      </div>

      <div class="section-content">
        <!-- Loading Suggestions -->
        <div class="suggestions-loading" *ngIf="isGeneratingReplies">
          <mat-icon class="spinning">psychology</mat-icon>
          <span>Generating AI suggestions...</span>
        </div>

        <!-- Suggested Replies List -->
        <div class="suggestions-list" *ngIf="!isGeneratingReplies && suggestedReplies.length > 0">
          <div 
            *ngFor="let reply of suggestedReplies; trackBy: trackBySuggestedReplyId"
            class="suggestion-item"
          >
            <div class="suggestion-content">
              <div class="suggestion-text">{{ reply.text }}</div>
              <div class="suggestion-meta">
                <span class="suggestion-type">{{ reply.type | titlecase }}</span>
                <span 
                  class="suggestion-confidence"
                  [style.color]="getConfidenceColor(reply.confidence)"
                >
                  {{ formatConfidence(reply.confidence) }}
                </span>
              </div>
              <div class="suggestion-context">{{ reply.context }}</div>
            </div>
            <div class="suggestion-actions">
              <button 
                mat-icon-button 
                class="use-suggestion-btn"
                (click)="onSelectSuggestedReply(reply)"
                matTooltip="Use this reply"
              >
                <mat-icon>send</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- No Suggestions -->
        <div class="no-suggestions" *ngIf="!isGeneratingReplies && suggestedReplies.length === 0">
          <mat-icon>lightbulb_outline</mat-icon>
          <span>No AI suggestions available</span>
        </div>
      </div>
    </div>

    <!-- Quick Responses Section -->
    <div class="assistant-section quick-responses-section">
      <div class="section-header">
        <mat-icon class="section-icon">flash_on</mat-icon>
        <h4 class="section-title">Quick Responses</h4>
      </div>

      <div class="section-content">
        <div class="quick-responses-grid">
          <button 
            *ngFor="let response of quickResponses"
            mat-button
            class="quick-response-btn"
            (click)="onUseQuickResponse(response)"
            [matTooltip]="response"
          >
            {{ response.length > 50 ? (response | slice:0:50) + '...' : response }}
          </button>
        </div>
      </div>
    </div>

    <!-- Auto-Reply Rules Section -->
    <div class="assistant-section rules-section">
      <div class="section-header">
        <mat-icon class="section-icon">rule</mat-icon>
        <h4 class="section-title">Auto-Reply Rules</h4>
        <button 
          mat-icon-button 
          class="add-rule-btn"
          (click)="onAddNewRule()"
          matTooltip="Add new rule"
        >
          <mat-icon>add</mat-icon>
        </button>
      </div>

      <div class="section-content">
        <div class="rules-list" *ngIf="autoReplyRules.length > 0; else noRules">
          <div 
            *ngFor="let rule of autoReplyRules; trackBy: trackByRuleId"
            class="rule-item"
            [class.disabled]="!rule.enabled"
          >
            <div class="rule-content">
              <div class="rule-header">
                <span class="rule-name">{{ rule.name }}</span>
                <div class="rule-actions">
                  <mat-slide-toggle
                    [checked]="rule.enabled"
                    (change)="onToggleRule(rule)"
                    color="primary"
                    class="rule-toggle"
                  ></mat-slide-toggle>
                  <button 
                    mat-icon-button 
                    class="rule-action-btn"
                    (click)="onEditRule(rule)"
                    matTooltip="Edit rule"
                  >
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button 
                    mat-icon-button 
                    class="rule-action-btn delete-btn"
                    (click)="onDeleteRule(rule)"
                    matTooltip="Delete rule"
                  >
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
              <div class="rule-details">
                <div class="rule-trigger">
                  <span class="rule-label">Trigger:</span>
                  <span class="rule-value">{{ rule.trigger }}</span>
                </div>
                <div class="rule-response">
                  <span class="rule-label">Response:</span>
                  <span class="rule-value">{{ rule.response }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noRules>
          <div class="no-rules">
            <mat-icon>rule</mat-icon>
            <span>No auto-reply rules configured</span>
            <button 
              mat-button 
              class="add-first-rule-btn"
              (click)="onAddNewRule()"
            >
              <mat-icon>add</mat-icon>
              Add First Rule
            </button>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
