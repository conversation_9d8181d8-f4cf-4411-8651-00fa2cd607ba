import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { Subject } from 'rxjs';

import {
  Conversation,
  CommunicationProvider
} from '../../models/conversation.models';

interface AutoReplyRule {
  id: string;
  name: string;
  trigger: string;
  response: string;
  enabled: boolean;
  conditions: string[];
  priority: number;
}

interface SuggestedReply {
  id: string;
  text: string;
  confidence: number;
  type: 'greeting' | 'question' | 'complaint' | 'compliment' | 'closing';
  context: string;
}

@Component({
  selector: 'app-ai-assistant',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatChipsModule
  ],
  templateUrl: './ai-assistant.component.html',
  styleUrls: ['./ai-assistant.component.scss']
})
export class AiAssistantComponent implements OnInit, OnDestroy, OnChanges {
  private destroy$ = new Subject<void>();

  @Input() conversation: Conversation | null = null;
  @Input() activeProvider: CommunicationProvider = 'email';
  @Input() isLoading = false;

  @Output() autoReplyGenerated = new EventEmitter<string>();
  @Output() replySelected = new EventEmitter<string>();

  // AI Auto-Reply Settings
  autoReplyEnabled = true;
  autoReplyDelay = 30; // seconds
  autoReplyMode: 'instant' | 'delayed' | 'manual' = 'delayed';

  // Suggested Replies
  suggestedReplies: SuggestedReply[] = [];
  isGeneratingReplies = false;

  // Auto-Reply Rules
  autoReplyRules: AutoReplyRule[] = [];

  // Real-time Analysis
  currentSentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
  urgencyLevel: 'low' | 'medium' | 'high' = 'medium';
  customerIntent: string = '';

  // Quick Responses
  quickResponses = [
    "Thank you for contacting us! I'll be happy to help you right away.",
    "I understand your concern and I'm here to help resolve this issue.",
    "I sincerely apologize for any inconvenience. Let me make this right for you.",
    "Thank you for your patience while I investigated this matter.",
    "I've found a solution for you. Here's what we can do:",
    "Your account has been updated and the changes are now active.",
    "I've escalated this to our specialist team for priority handling.",
    "Is there anything else I can assist you with today?",
    "I'm glad I could help resolve this for you!",
    "Let me connect you with the right department for this request."
  ];

  constructor() {}

  ngOnInit(): void {
    this.initializeAutoReplyRules();
    this.loadSuggestedReplies();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(): void {
    if (this.conversation) {
      this.analyzeConversation();
      this.generateContextualReplies();
    }
  }

  private initializeAutoReplyRules(): void {
    this.autoReplyRules = [
      {
        id: '1',
        name: 'Greeting Response',
        trigger: 'hello|hi|hey|good morning|good afternoon',
        response: 'Hello! Thank you for contacting us. How can I help you today?',
        enabled: true,
        conditions: ['first_message'],
        priority: 1
      },
      {
        id: '2',
        name: 'Order Status Inquiry',
        trigger: 'order status|where is my order|tracking',
        response: 'I\'d be happy to help you check your order status. Could you please provide your order number?',
        enabled: true,
        conditions: ['contains_order_keywords'],
        priority: 2
      },
      {
        id: '3',
        name: 'Billing Question',
        trigger: 'billing|payment|charge|refund',
        response: 'I can help you with billing questions. Let me look into your account details.',
        enabled: true,
        conditions: ['contains_billing_keywords'],
        priority: 2
      },
      {
        id: '4',
        name: 'Technical Support',
        trigger: 'not working|error|problem|issue|bug',
        response: 'I\'m sorry to hear you\'re experiencing technical difficulties. Let me help you troubleshoot this issue.',
        enabled: true,
        conditions: ['contains_technical_keywords'],
        priority: 3
      },
      {
        id: '5',
        name: 'Thank You Response',
        trigger: 'thank you|thanks|appreciate',
        response: 'You\'re very welcome! I\'m glad I could help. Is there anything else you need assistance with?',
        enabled: true,
        conditions: ['customer_gratitude'],
        priority: 1
      }
    ];
  }

  private loadSuggestedReplies(): void {
    this.isGeneratingReplies = true;

    // Simulate AI generating contextual replies
    setTimeout(() => {
      const contextualReplies: SuggestedReply[] = [
        {
          id: '1',
          text: 'Thank you for reaching out! I\'ll be happy to assist you with your inquiry today.',
          confidence: 0.95,
          type: 'greeting',
          context: 'Professional greeting for new conversation'
        },
        {
          id: '2',
          text: 'I understand your concern about the order delay. Let me check the tracking status immediately and provide you with an update.',
          confidence: 0.92,
          type: 'question',
          context: 'Response to order status inquiry with proactive action'
        },
        {
          id: '3',
          text: 'I sincerely apologize for the inconvenience you\'ve experienced. Here are the immediate steps I\'ll take to resolve this issue:',
          confidence: 0.89,
          type: 'complaint',
          context: 'Empathetic acknowledgment with solution-focused approach'
        },
        {
          id: '4',
          text: 'Great question! Our premium features include advanced analytics, priority support, and custom integrations. Would you like me to schedule a demo?',
          confidence: 0.87,
          type: 'question',
          context: 'Product inquiry with value proposition and next step'
        },
        {
          id: '5',
          text: 'I\'ve processed your refund request. You should see the credit back to your original payment method within 3-5 business days.',
          confidence: 0.84,
          type: 'closing',
          context: 'Resolution confirmation with clear timeline'
        },
        {
          id: '6',
          text: 'Thank you for your patience! I\'ve escalated this to our technical team and you\'ll receive an update within 2 hours.',
          confidence: 0.81,
          type: 'compliment',
          context: 'Escalation notification with commitment timeline'
        }
      ];

      // Select 3-4 random replies to simulate AI selection
      this.suggestedReplies = contextualReplies
        .sort(() => Math.random() - 0.5)
        .slice(0, Math.floor(Math.random() * 2) + 3);

      this.isGeneratingReplies = false;
    }, 1500);
  }

  private analyzeConversation(): void {
    if (!this.conversation) return;

    // Simulate real-time conversation analysis
    this.currentSentiment = this.detectSentiment();
    this.urgencyLevel = this.detectUrgency();
    this.customerIntent = this.detectIntent();
  }

  private detectSentiment(): 'positive' | 'neutral' | 'negative' {
    // Simulate sentiment analysis
    const sentiments = ['positive', 'neutral', 'negative'] as const;
    return sentiments[Math.floor(Math.random() * sentiments.length)];
  }

  private detectUrgency(): 'low' | 'medium' | 'high' {
    // Simulate urgency detection
    const urgencies = ['low', 'medium', 'high'] as const;
    return urgencies[Math.floor(Math.random() * urgencies.length)];
  }

  private detectIntent(): string {
    const intents = [
      'Order inquiry',
      'Technical support',
      'Billing question',
      'Product information',
      'Complaint resolution'
    ];
    return intents[Math.floor(Math.random() * intents.length)];
  }

  private generateContextualReplies(): void {
    // Generate replies based on conversation context
    this.loadSuggestedReplies();
  }

  onToggleAutoReply(): void {
    console.log('Auto-reply toggled:', this.autoReplyEnabled);
  }

  onAutoReplyModeChange(): void {
    console.log('Auto-reply mode changed:', this.autoReplyMode);
  }

  onDelayChange(): void {
    console.log('Auto-reply delay changed:', this.autoReplyDelay);
  }

  onSelectSuggestedReply(reply: SuggestedReply): void {
    this.replySelected.emit(reply.text);
  }

  onUseQuickResponse(response: string): void {
    this.replySelected.emit(response);
  }

  onGenerateNewReplies(): void {
    this.loadSuggestedReplies();
  }

  onToggleRule(rule: AutoReplyRule): void {
    rule.enabled = !rule.enabled;
    console.log('Rule toggled:', rule.name, rule.enabled);
  }

  onEditRule(rule: AutoReplyRule): void {
    console.log('Edit rule:', rule.name);
    // This would open a modal to edit the rule
  }

  onDeleteRule(rule: AutoReplyRule): void {
    const index = this.autoReplyRules.findIndex(r => r.id === rule.id);
    if (index > -1) {
      this.autoReplyRules.splice(index, 1);
    }
  }

  onAddNewRule(): void {
    console.log('Add new auto-reply rule');
    // This would open a modal to create a new rule
  }

  getSentimentIcon(): string {
    switch (this.currentSentiment) {
      case 'positive': return 'sentiment_very_satisfied';
      case 'negative': return 'sentiment_very_dissatisfied';
      default: return 'sentiment_neutral';
    }
  }

  getSentimentColor(): string {
    switch (this.currentSentiment) {
      case 'positive': return '#4caf50';
      case 'negative': return '#f44336';
      default: return '#ff9800';
    }
  }

  getUrgencyColor(): string {
    switch (this.urgencyLevel) {
      case 'high': return '#f44336';
      case 'medium': return '#ff9800';
      default: return '#4caf50';
    }
  }

  getConfidenceColor(confidence: number): string {
    if (confidence >= 0.8) return '#4caf50';
    if (confidence >= 0.6) return '#ff9800';
    return '#f44336';
  }

  formatConfidence(confidence: number): string {
    return `${Math.round(confidence * 100)}%`;
  }

  trackBySuggestedReplyId(_index: number, reply: SuggestedReply): string {
    return reply.id;
  }

  trackByRuleId(_index: number, rule: AutoReplyRule): string {
    return rule.id;
  }
}
