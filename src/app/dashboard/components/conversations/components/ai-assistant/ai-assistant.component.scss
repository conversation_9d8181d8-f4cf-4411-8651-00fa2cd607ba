// AI Assistant Component - Futuristic Dark Theme
.ai-assistant {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  position: relative;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Loading State
.assistant-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.7);

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #ab47bc;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 0.9rem;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// No Conversation State
.no-conversation {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .no-conversation-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    max-width: 250px;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #ab47bc;
      opacity: 0.7;
      margin-bottom: 1rem;
    }

    h4 {
      font-size: 1.1rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.85rem;
      line-height: 1.4;
    }
  }
}

// Assistant Content
.assistant-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
}

// Assistant Section
.assistant-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    background: rgba(255, 255, 255, 0.02);

    .section-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      color: #ab47bc;
    }

    .section-title {
      flex: 1;
      font-size: 0.9rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
    }

    .refresh-btn,
    .add-rule-btn {
      min-width: 32px;
      min-height: 32px;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;

        &.spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .section-content {
    padding: 1rem;
  }
}

// Settings Section
.settings-section {
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .setting-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.2rem;

      .setting-label {
        font-size: 0.85rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }

      .setting-description {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .mode-select,
    .delay-input {
      width: 120px;

      ::ng-deep {
        .mat-mdc-form-field-outline {
          color: rgba(255, 255, 255, 0.2);
        }

        .mdc-text-field--outlined .mdc-notched-outline__leading,
        .mdc-text-field--outlined .mdc-notched-outline__notch,
        .mdc-text-field--outlined .mdc-notched-outline__trailing {
          border-color: rgba(255, 255, 255, 0.15);
        }

        input,
        .mat-mdc-select-value {
          color: #ffffff;
          font-size: 0.8rem;
        }
      }
    }

    ::ng-deep .mat-mdc-slide-toggle {
      .mdc-switch__track {
        background: rgba(255, 255, 255, 0.2);
      }

      &.mat-checked .mdc-switch__track {
        background: rgba(171, 71, 188, 0.5);
      }

      .mdc-switch__handle-track {
        background: #ab47bc;
      }
    }
  }
}

// Analysis Section
.analysis-section {
  .analysis-item {
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .analysis-header {
      display: flex;
      align-items: center;
      gap: 0.8rem;

      .analysis-icon {
        font-size: 1.3rem;
        width: 1.3rem;
        height: 1.3rem;
      }

      .analysis-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.2rem;

        .analysis-label {
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.7);
        }

        .analysis-value {
          font-size: 0.9rem;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.95);
        }
      }
    }
  }
}

// Suggestions Section
.suggestions-section {
  .suggestions-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    padding: 1.5rem;
    color: rgba(255, 255, 255, 0.7);

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      color: #ab47bc;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 0.85rem;
    }
  }

  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;

    .suggestion-item {
      display: flex;
      gap: 0.8rem;
      padding: 0.8rem 1rem;
      margin-bottom: 0.8rem;
      background: rgba(171, 71, 188, 0.05);
      border: 1px solid rgba(171, 71, 188, 0.15);
      border-radius: 8px;
      transition: all 200ms ease;
      backdrop-filter: blur(10px);
      min-height: 44px;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(171, 71, 188, 0.25);
        transform: translateY(-1px);
      }

      .suggestion-content {
        flex: 1;
        min-width: 0;

        .suggestion-text {
          font-size: 0.85rem;
          color: rgba(255, 255, 255, 0.9);
          line-height: 1.4;
          margin-bottom: 0.5rem;
        }

        .suggestion-meta {
          display: flex;
          align-items: center;
          gap: 0.8rem;
          margin-bottom: 0.3rem;

          .suggestion-type {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .suggestion-confidence {
            font-size: 0.7rem;
            font-weight: 600;
          }
        }

        .suggestion-context {
          font-size: 0.75rem;
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
        }
      }

      .suggestion-actions {
        display: flex;
        align-items: flex-start;

        .use-suggestion-btn {
          min-width: 36px;
          min-height: 36px;
          background: rgba(171, 71, 188, 0.2);
          color: #ab47bc;
          border-radius: 6px;

          &:hover {
            background: rgba(171, 71, 188, 0.3);
          }

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }

  .no-suggestions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      opacity: 0.5;
    }

    span {
      font-size: 0.85rem;
    }
  }
}
