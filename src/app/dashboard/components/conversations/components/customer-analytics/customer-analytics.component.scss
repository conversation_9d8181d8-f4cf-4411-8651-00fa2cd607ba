// Customer Analytics Component - Futuristic Dark Theme
.customer-analytics {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  position: relative;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Loading State
.analytics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.7);

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 0.9rem;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// No Customer State
.no-customer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .no-customer-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    max-width: 250px;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      opacity: 0.5;
      margin-bottom: 1rem;
    }

    h4 {
      font-size: 1.1rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.85rem;
      line-height: 1.4;
    }
  }
}

// Analytics Content
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
}

// Analytics Section
.analytics-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    &:hover {
      background: rgba(255, 255, 255, 0.03);
    }

    &.expanded {
      background: rgba(255, 255, 255, 0.05);
    }

    .section-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      color: rgba(255, 255, 255, 0.7);
    }

    .section-title {
      flex: 1;
      font-size: 0.9rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
    }

    .expand-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
      color: rgba(255, 255, 255, 0.6);
      transition: transform 0.2s ease;
    }

    &.expanded .expand-icon {
      transform: rotate(180deg);
    }
  }

  .section-content {
    padding: 1rem;
    animation: slideDown 0.2s ease-out;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Customer Profile
.customer-profile {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;

  .customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      font-size: 1.5rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .customer-details {
    flex: 1;
    min-width: 0;

    .customer-name {
      font-size: 1.1rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
      margin: 0 0 0.5rem 0;
    }

    .customer-contact {
      display: flex;
      flex-direction: column;
      gap: 0.3rem;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);

        mat-icon {
          font-size: 0.9rem;
          width: 0.9rem;
          height: 0.9rem;
          color: rgba(255, 255, 255, 0.6);
        }

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// Customer Stats
.customer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;

  .stat-item {
    text-align: center;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;

    .stat-value {
      font-size: 1.1rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
      margin-bottom: 0.2rem;
    }

    .stat-label {
      font-size: 0.7rem;
      color: rgba(255, 255, 255, 0.6);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Satisfaction Score
.satisfaction-score {
  margin-bottom: 1.5rem;

  .score-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;

    .score-label {
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.8);
    }

    .score-value {
      font-size: 0.9rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .score-stars {
    display: flex;
    gap: 0.2rem;

    mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      color: rgba(255, 255, 255, 0.3);

      &.filled {
        color: #ffc107;
      }
    }
  }
}

// Customer Tags
.customer-tags {
  margin-bottom: 1.5rem;

  .tags-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;

    .tag {
      background: rgba(33, 150, 243, 0.2);
      color: rgba(33, 150, 243, 0.9);
      padding: 0.2rem 0.6rem;
      border-radius: 12px;
      font-size: 0.75rem;
      border: 1px solid rgba(33, 150, 243, 0.3);
    }
  }
}

// Profile Actions
.profile-actions {
  display: flex;
  gap: 0.5rem;

  .action-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
    min-height: 36px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
    }

    mat-icon {
      margin-right: 0.3rem;
      font-size: 0.9rem;
      width: 0.9rem;
      height: 0.9rem;
    }
  }
}

// Sentiment Analysis
.overall-sentiment {
  margin-bottom: 1.5rem;

  .sentiment-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.8rem;

    .sentiment-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .sentiment-info {
      flex: 1;

      .sentiment-label {
        font-size: 1rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 0.2rem;
      }

      .sentiment-confidence {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .sentiment-progress {
    ::ng-deep .mat-mdc-progress-bar {
      height: 6px;
      border-radius: 3px;
      background: rgba(255, 255, 255, 0.1);

      .mat-mdc-progress-bar-fill::after {
        background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
      }
    }
  }
}

.sentiment-keywords {
  .keywords-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .keywords-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .keyword-item {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 6px;

      .keyword-text {
        flex: 1;
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.9);
      }

      .keyword-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .keyword-frequency {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
        min-width: 20px;
        text-align: center;
      }
    }
  }
}

// Interaction History
.history-timeline {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: rgba(255, 255, 255, 0.1);
  }

  .timeline-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    .timeline-marker {
      position: relative;
      z-index: 1;

      .timeline-icon {
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
      }
    }

    .timeline-content {
      flex: 1;
      min-width: 0;

      .timeline-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 0.3rem;

        .timeline-title {
          font-size: 0.85rem;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
        }

        .timeline-time {
          font-size: 0.7rem;
          color: rgba(255, 255, 255, 0.6);
          white-space: nowrap;
        }
      }

      .timeline-description {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.3;
        margin: 0 0 0.5rem 0;
      }

      .timeline-meta {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        font-size: 0.7rem;

        .timeline-platform {
          color: rgba(255, 255, 255, 0.6);
        }

        .timeline-status {
          padding: 0.1rem 0.4rem;
          border-radius: 8px;
          text-transform: capitalize;

          &.completed,
          &.resolved {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
          }

          &.pending {
            background: rgba(255, 152, 0, 0.2);
            color: #ff9800;
          }

          &.failed {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
          }
        }

        .timeline-amount {
          color: rgba(76, 175, 80, 0.9);
          font-weight: 600;
        }
      }
    }
  }
}

.no-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;

  mat-icon {
    font-size: 2rem;
    width: 2rem;
    height: 2rem;
    opacity: 0.5;
  }

  span {
    font-size: 0.85rem;
  }
}

// Purchase History
.purchase-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;

  .purchase-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;

    .purchase-info {
      flex: 1;
      min-width: 0;

      .purchase-name {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.2rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .purchase-date {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .purchase-amount {
      font-size: 0.85rem;
      font-weight: 600;
      color: rgba(76, 175, 80, 0.9);
    }

    .purchase-status {
      padding: 0.2rem 0.5rem;
      border-radius: 8px;
      font-size: 0.7rem;
      text-transform: capitalize;

      &.completed {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
      }

      &.pending {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
      }

      &.refunded {
        background: rgba(244, 67, 54, 0.2);
        color: #f44336;
      }
    }
  }
}

// Mobile Responsive Design
@media (max-width: 768px) {
  .customer-profile {
    flex-direction: column;
    align-items: center;
    text-align: center;

    .customer-avatar {
      width: 80px;
      height: 80px;
    }

    .customer-details {
      .customer-contact {
        align-items: center;
      }
    }
  }

  .customer-stats {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .profile-actions {
    flex-direction: column;

    .action-btn {
      min-height: 44px;
    }
  }

  .timeline-item {
    .timeline-content {
      .timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.2rem;
      }

      .timeline-meta {
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }

  .purchase-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;

    .purchase-info {
      width: 100%;
    }

    .purchase-amount,
    .purchase-status {
      align-self: flex-end;
    }
  }

  .section-header {
    padding: 0.8rem;

    .section-title {
      font-size: 0.85rem;
    }
  }

  .section-content {
    padding: 0.8rem;
  }
}

// Performance optimizations
.customer-analytics,
.analytics-section,
.timeline-item {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .analytics-section,
  .section-content,
  .timeline-item {
    transition: none !important;
    animation: none !important;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes slideDown {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
