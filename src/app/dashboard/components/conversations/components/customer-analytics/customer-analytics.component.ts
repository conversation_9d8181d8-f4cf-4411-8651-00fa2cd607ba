import { Component, Input, OnInit, <PERSON><PERSON><PERSON>roy, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Subject } from 'rxjs';

import { 
  Customer, 
  Conversation, 
  InteractionHistoryItem,
  SentimentAnalysis 
} from '../../models/conversation.models';

@Component({
  selector: 'app-customer-analytics',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatExpansionModule,
    MatProgressBarModule
  ],
  templateUrl: './customer-analytics.component.html',
  styleUrls: ['./customer-analytics.component.scss']
})
export class CustomerAnalyticsComponent implements OnInit, OnDestroy, OnChanges {
  private destroy$ = new Subject<void>();

  @Input() customer: Customer | null = null;
  @Input() conversation: Conversation | null = null;
  @Input() isLoading = false;

  // Analytics data
  interactionHistory: InteractionHistoryItem[] = [];
  sentimentAnalysis: SentimentAnalysis | null = null;
  
  // UI state
  expandedSections: Set<string> = new Set(['profile']);

  constructor() {}

  ngOnInit(): void {
    this.loadAnalytics();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(): void {
    if (this.customer) {
      this.loadAnalytics();
    }
  }

  private loadAnalytics(): void {
    if (!this.customer) return;

    // Load interaction history
    this.loadInteractionHistory();
    
    // Load sentiment analysis
    this.loadSentimentAnalysis();
  }

  private loadInteractionHistory(): void {
    // Simulate loading interaction history
    this.interactionHistory = [
      {
        id: '1',
        type: 'message',
        title: 'Email inquiry',
        description: 'Asked about order status',
        timestamp: new Date(Date.now() - 86400000),
        platform: 'Email',
        status: 'resolved',
        metadata: {}
      },
      {
        id: '2',
        type: 'purchase',
        title: 'Product purchase',
        description: 'Bought premium subscription',
        timestamp: new Date(Date.now() - 172800000),
        platform: 'Website',
        status: 'completed',
        metadata: { amount: 99.99 }
      },
      {
        id: '3',
        type: 'support',
        title: 'Technical support',
        description: 'Login issues resolved',
        timestamp: new Date(Date.now() - 259200000),
        platform: 'Chat',
        status: 'resolved',
        metadata: {}
      }
    ];
  }

  private loadSentimentAnalysis(): void {
    // Simulate sentiment analysis data
    this.sentimentAnalysis = {
      overall: 'positive',
      confidence: 0.85,
      trend: [
        { timestamp: new Date(Date.now() - 86400000), sentiment: 'neutral', score: 0.6 },
        { timestamp: new Date(Date.now() - 43200000), sentiment: 'positive', score: 0.8 },
        { timestamp: new Date(), sentiment: 'positive', score: 0.9 }
      ],
      keywords: [
        { word: 'helpful', sentiment: 'positive', frequency: 5 },
        { word: 'quick', sentiment: 'positive', frequency: 3 },
        { word: 'issue', sentiment: 'neutral', frequency: 2 }
      ]
    };
  }

  toggleSection(section: string): void {
    if (this.expandedSections.has(section)) {
      this.expandedSections.delete(section);
    } else {
      this.expandedSections.add(section);
    }
  }

  isSectionExpanded(section: string): boolean {
    return this.expandedSections.has(section);
  }

  getSatisfactionStars(score: number): number[] {
    return Array(5).fill(0).map((_, i) => i < score ? 1 : 0);
  }

  getSentimentColor(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return '#4caf50';
      case 'negative': return '#f44336';
      case 'neutral': return '#ff9800';
      default: return '#9e9e9e';
    }
  }

  getSentimentIcon(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return 'sentiment_very_satisfied';
      case 'negative': return 'sentiment_very_dissatisfied';
      case 'neutral': return 'sentiment_neutral';
      default: return 'help';
    }
  }

  getInteractionIcon(type: string): string {
    switch (type) {
      case 'message': return 'message';
      case 'call': return 'phone';
      case 'meeting': return 'video_call';
      case 'ticket': return 'support';
      case 'purchase': return 'shopping_cart';
      case 'support': return 'help_center';
      default: return 'history';
    }
  }

  getInteractionColor(type: string): string {
    switch (type) {
      case 'message': return '#2196f3';
      case 'call': return '#4caf50';
      case 'meeting': return '#9c27b0';
      case 'ticket': return '#ff9800';
      case 'purchase': return '#00bcd4';
      case 'support': return '#f44336';
      default: return '#9e9e9e';
    }
  }

  formatTimestamp(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / 86400000);
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    
    return date.toLocaleDateString();
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getTotalPurchaseValue(): number {
    if (!this.customer?.purchaseHistory) return 0;
    return this.customer.purchaseHistory
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);
  }

  getAverageResponseTime(): string {
    // Calculate from conversation history
    return '2.3 min';
  }

  getResolutionRate(): number {
    if (!this.customer?.ticketHistory) return 0;
    const resolved = this.customer.ticketHistory.filter(t => t.status === 'resolved').length;
    return this.customer.ticketHistory.length > 0 ? (resolved / this.customer.ticketHistory.length) * 100 : 0;
  }

  onViewFullProfile(): void {
    // Emit event or navigate to full customer profile
    console.log('View full profile for customer:', this.customer?.id);
  }

  onExportData(): void {
    // Export customer analytics data
    console.log('Export analytics data for customer:', this.customer?.id);
  }

  trackByHistoryId(_index: number, item: InteractionHistoryItem): string {
    return item.id;
  }
}
