<!-- Customer Analytics Component -->
<div class="customer-analytics" [class.loading]="isLoading">
  <!-- Loading State -->
  <div class="analytics-loading" *ngIf="isLoading">
    <div class="loading-content">
      <mat-icon class="spinning">refresh</mat-icon>
      <span>Loading analytics...</span>
    </div>
  </div>

  <!-- No Customer Selected -->
  <div class="no-customer" *ngIf="!customer && !isLoading">
    <div class="no-customer-content">
      <mat-icon>person_outline</mat-icon>
      <h4>No customer selected</h4>
      <p>Select a conversation to view customer analytics and insights.</p>
    </div>
  </div>

  <!-- Customer Analytics Content -->
  <div class="analytics-content" *ngIf="customer && !isLoading">
    <!-- Customer Profile Section -->
    <div class="analytics-section profile-section">
      <div 
        class="section-header"
        (click)="toggleSection('profile')"
        [class.expanded]="isSectionExpanded('profile')"
      >
        <mat-icon class="section-icon">person</mat-icon>
        <h4 class="section-title">Customer Profile</h4>
        <mat-icon class="expand-icon">{{ isSectionExpanded('profile') ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>

      <div class="section-content" *ngIf="isSectionExpanded('profile')">
        <!-- Customer Avatar and Basic Info -->
        <div class="customer-profile">
          <div class="customer-avatar">
            <img 
              *ngIf="customer.avatar; else defaultAvatar" 
              [src]="customer.avatar" 
              [alt]="customer.name"
            />
            <ng-template #defaultAvatar>
              <div class="avatar-placeholder">
                {{ customer.name.charAt(0).toUpperCase() }}
              </div>
            </ng-template>
          </div>
          <div class="customer-details">
            <h5 class="customer-name">{{ customer.name }}</h5>
            <div class="customer-contact">
              <div class="contact-item">
                <mat-icon>email</mat-icon>
                <span>{{ customer.email }}</span>
              </div>
              <div class="contact-item" *ngIf="customer.phone">
                <mat-icon>phone</mat-icon>
                <span>{{ customer.phone }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer Stats -->
        <div class="customer-stats">
          <div class="stat-item">
            <div class="stat-value">{{ customer.totalInteractions }}</div>
            <div class="stat-label">Total Interactions</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ getTotalPurchaseValue() | currency }}</div>
            <div class="stat-label">Total Spent</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ getResolutionRate() }}%</div>
            <div class="stat-label">Resolution Rate</div>
          </div>
        </div>

        <!-- Satisfaction Score -->
        <div class="satisfaction-score">
          <div class="score-header">
            <span class="score-label">Satisfaction Score</span>
            <span class="score-value">{{ customer.satisfactionScore }}/5</span>
          </div>
          <div class="score-stars">
            <mat-icon 
              *ngFor="let star of getSatisfactionStars(customer.satisfactionScore)"
              [class.filled]="star === 1"
            >
              {{ star === 1 ? 'star' : 'star_border' }}
            </mat-icon>
          </div>
        </div>

        <!-- Customer Tags -->
        <div class="customer-tags" *ngIf="customer.tags.length > 0">
          <div class="tags-header">
            <mat-icon>label</mat-icon>
            <span>Tags</span>
          </div>
          <div class="tags-list">
            <span 
              *ngFor="let tag of customer.tags" 
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Profile Actions -->
        <div class="profile-actions">
          <button 
            mat-button 
            class="action-btn"
            (click)="onViewFullProfile()"
          >
            <mat-icon>open_in_new</mat-icon>
            View Full Profile
          </button>
          <button 
            mat-button 
            class="action-btn"
            (click)="onExportData()"
          >
            <mat-icon>download</mat-icon>
            Export Data
          </button>
        </div>
      </div>
    </div>

    <!-- Sentiment Analysis Section -->
    <div class="analytics-section sentiment-section" *ngIf="sentimentAnalysis">
      <div 
        class="section-header"
        (click)="toggleSection('sentiment')"
        [class.expanded]="isSectionExpanded('sentiment')"
      >
        <mat-icon class="section-icon">psychology</mat-icon>
        <h4 class="section-title">Sentiment Analysis</h4>
        <mat-icon class="expand-icon">{{ isSectionExpanded('sentiment') ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>

      <div class="section-content" *ngIf="isSectionExpanded('sentiment')">
        <!-- Overall Sentiment -->
        <div class="overall-sentiment">
          <div class="sentiment-indicator">
            <mat-icon 
              class="sentiment-icon"
              [style.color]="getSentimentColor(sentimentAnalysis.overall)"
            >
              {{ getSentimentIcon(sentimentAnalysis.overall) }}
            </mat-icon>
            <div class="sentiment-info">
              <div class="sentiment-label">{{ sentimentAnalysis.overall | titlecase }}</div>
              <div class="sentiment-confidence">{{ (sentimentAnalysis.confidence * 100) | number:'1.0-0' }}% confidence</div>
            </div>
          </div>
          <div class="sentiment-progress">
            <mat-progress-bar
              mode="determinate"
              [value]="sentimentAnalysis.confidence * 100"
              [color]="'primary'"
            ></mat-progress-bar>
          </div>
        </div>

        <!-- Sentiment Keywords -->
        <div class="sentiment-keywords" *ngIf="sentimentAnalysis.keywords.length > 0">
          <div class="keywords-header">
            <mat-icon>tag</mat-icon>
            <span>Key Sentiment Words</span>
          </div>
          <div class="keywords-list">
            <div 
              *ngFor="let keyword of sentimentAnalysis.keywords.slice(0, 5)"
              class="keyword-item"
            >
              <span class="keyword-text">{{ keyword.word }}</span>
              <div 
                class="keyword-indicator"
                [style.background-color]="getSentimentColor(keyword.sentiment)"
              ></div>
              <span class="keyword-frequency">{{ keyword.frequency }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Interaction History Section -->
    <div class="analytics-section history-section">
      <div 
        class="section-header"
        (click)="toggleSection('history')"
        [class.expanded]="isSectionExpanded('history')"
      >
        <mat-icon class="section-icon">history</mat-icon>
        <h4 class="section-title">Interaction History</h4>
        <mat-icon class="expand-icon">{{ isSectionExpanded('history') ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>

      <div class="section-content" *ngIf="isSectionExpanded('history')">
        <div class="history-timeline" *ngIf="interactionHistory.length > 0; else noHistory">
          <div 
            *ngFor="let item of interactionHistory; trackBy: trackByHistoryId"
            class="timeline-item"
          >
            <div class="timeline-marker">
              <mat-icon 
                class="timeline-icon"
                [style.color]="getInteractionColor(item.type)"
              >
                {{ getInteractionIcon(item.type) }}
              </mat-icon>
            </div>
            <div class="timeline-content">
              <div class="timeline-header">
                <h6 class="timeline-title">{{ item.title }}</h6>
                <span class="timeline-time">{{ formatTimestamp(item.timestamp) }}</span>
              </div>
              <p class="timeline-description">{{ item.description }}</p>
              <div class="timeline-meta">
                <span class="timeline-platform">{{ item.platform }}</span>
                <span class="timeline-status" [class]="item.status">{{ item.status }}</span>
                <span
                  class="timeline-amount"
                  *ngIf="item.metadata['amount']"
                >
                  {{ formatCurrency(item.metadata['amount']) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noHistory>
          <div class="no-history">
            <mat-icon>history</mat-icon>
            <span>No interaction history available</span>
          </div>
        </ng-template>
      </div>
    </div>

    <!-- Purchase History Section -->
    <div class="analytics-section purchase-section" *ngIf="customer.purchaseHistory.length > 0">
      <div 
        class="section-header"
        (click)="toggleSection('purchases')"
        [class.expanded]="isSectionExpanded('purchases')"
      >
        <mat-icon class="section-icon">shopping_cart</mat-icon>
        <h4 class="section-title">Purchase History</h4>
        <mat-icon class="expand-icon">{{ isSectionExpanded('purchases') ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>

      <div class="section-content" *ngIf="isSectionExpanded('purchases')">
        <div class="purchase-list">
          <div 
            *ngFor="let purchase of customer.purchaseHistory.slice(0, 5)"
            class="purchase-item"
          >
            <div class="purchase-info">
              <div class="purchase-name">{{ purchase.productName }}</div>
              <div class="purchase-date">{{ purchase.date | date:'short' }}</div>
            </div>
            <div class="purchase-amount">
              {{ formatCurrency(purchase.amount) }}
            </div>
            <div 
              class="purchase-status"
              [class]="purchase.status"
            >
              {{ purchase.status }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
