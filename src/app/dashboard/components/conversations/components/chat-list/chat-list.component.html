<!-- Chat List Component -->
<div class="chat-list-container">

  <!-- Search and Sort Section -->
  <div class="search-sort-section">
    <!-- Search Input -->
   

  <!-- Filter Toggle and Active Filters -->
  <div class="filter-header">
    <button 
      mat-icon-button 
      class="filter-toggle"
      (click)="toggleFilters()"
      [class.active]="showFilters"
      [attr.aria-label]="showFilters ? 'Hide filters' : 'Show filters'"
    >
      <mat-icon>filter_list</mat-icon>
      <span class="filter-count" *ngIf="getActiveFilterCount() > 0">{{ getActiveFilterCount() }}</span>
    </button>

    <div class="active-filters" *ngIf="hasActiveFilters()">
      <button 
        mat-button 
        class="clear-filters-btn"
        (click)="clearFilters()"
        size="small"
      >
        <mat-icon>clear</mat-icon>
        Clear filters
      </button>
    </div>
  </div>

  <!-- Expandable Filters Panel -->
  <div class="filters-panel" *ngIf="showFilters" [@slideDown]>
    <div class="filter-group">
      <label class="filter-label">Status</label>
      <div class="filter-options">
        <button
          *ngFor="let status of statusOptions"
          mat-button
          class="filter-chip"
          [class.active]="activeFilters.status?.includes(status.value)"
          (click)="toggleStatusFilter(status.value)"
        >
          <div class="status-indicator" [style.background-color]="status.color"></div>
          {{ status.label }}
        </button>
      </div>
    </div>

    <div class="filter-group">
      <label class="filter-label">Priority</label>
      <div class="filter-options">
        <button 
          *ngFor="let priority of priorityOptions"
          mat-button
          class="filter-chip"
          [class.active]="activeFilters.priority?.includes(priority.value)"
          (click)="togglePriorityFilter(priority.value)"
        >
          <div class="priority-indicator" [style.background-color]="priority.color"></div>
          {{ priority.label }}
        </button>
      </div>
    </div>
  </div>

  <!-- Conversations List -->
  <div class="conversations-list" *ngIf="conversations.length > 0; else noConversations">
    <div 
      *ngFor="let conversation of conversations; trackBy: trackByConversationId"
      class="conversation-item"
      [class.selected]="isSelected(conversation)"
      [class.unread]="conversation.unreadCount > 0"
      (click)="onConversationClick(conversation)"
      [matMenuTriggerFor]="contextMenu"
      #menuTrigger="matMenuTrigger"
      (contextmenu)="$event.preventDefault(); menuTrigger.openMenu()"
    >
      <!-- Customer Avatar and Platform Indicator -->
      <div class="conversation-avatar">
        <div class="customer-avatar">
          <img 
            *ngIf="conversation.customer.avatar; else defaultAvatar" 
            [src]="conversation.customer.avatar" 
            [alt]="conversation.customer.name"
            class="avatar-image"
          />
          <ng-template #defaultAvatar>
            <div class="avatar-placeholder">
              {{ conversation.customer.name.charAt(0).toUpperCase() }}
            </div>
          </ng-template>
        </div>
        <div 
          class="platform-indicator"
          [style.color]="getPlatformColor(conversation.platform)"
          [title]="conversation.platform"
        >
          <mat-icon>{{ getPlatformIcon(conversation.platform) }}</mat-icon>
        </div>
      </div>

      <!-- Conversation Details -->
      <div class="conversation-details">
        <div class="conversation-header">
          <div class="customer-info">
            <span class="customer-name">{{ conversation.customer.name }}</span>
            <div class="status-priority">
              <div 
                class="status-badge"
                [style.background-color]="getStatusColor(conversation.status)"
                [title]="conversation.status"
              ></div>
              <div 
                class="priority-badge"
                [style.background-color]="getPriorityColor(conversation.priority)"
                [title]="conversation.priority + ' priority'"
              ></div>
            </div>
          </div>
          <div class="conversation-meta">
            <span class="last-message-time">
              {{ formatLastMessageTime(conversation.lastMessageAt) }}
            </span>
            <div class="unread-count" *ngIf="conversation.unreadCount > 0">
              {{ conversation.unreadCount }}
            </div>
          </div>
        </div>

        <div class="conversation-preview">
          <span class="subject">{{ conversation.subject }}</span>
          <div class="tags" *ngIf="conversation.tags.length > 0">
            <span 
              *ngFor="let tag of conversation.tags.slice(0, 2)" 
              class="tag"
            >
              {{ tag }}
            </span>
            <span *ngIf="conversation.tags.length > 2" class="tag-more">
              +{{ conversation.tags.length - 2 }}
            </span>
          </div>
        </div>

        <!-- AI Summary Preview -->
        <div class="ai-summary" *ngIf="conversation.aiSummary">
          <mat-icon class="ai-icon">psychology</mat-icon>
          <span class="summary-text">{{ conversation.aiSummary.summary }}</span>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <button 
          mat-icon-button 
          class="quick-action-btn"
          (click)="onMarkAsRead(conversation, $event)"
          *ngIf="conversation.unreadCount > 0"
          matTooltip="Mark as read"
        >
          <mat-icon>mark_email_read</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          class="quick-action-btn priority-urgent"
          *ngIf="conversation.priority === 'urgent'"
          matTooltip="Urgent priority"
        >
          <mat-icon>priority_high</mat-icon>
        </button>
      </div>
    </div>

    <!-- Context Menu -->
    <mat-menu #contextMenu="matMenu" class="conversation-context-menu">
      <button mat-menu-item (click)="onMarkAsRead(selectedConversation!, $event)">
        <mat-icon>mark_email_read</mat-icon>
        <span>Mark as Read</span>
      </button>
      <button mat-menu-item (click)="onArchiveConversation(selectedConversation!, $event)">
        <mat-icon>archive</mat-icon>
        <span>Archive</span>
      </button>
      <button mat-menu-item (click)="onAssignAgent(selectedConversation!, $event)">
        <mat-icon>person_add</mat-icon>
        <span>Assign Agent</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item [matMenuTriggerFor]="priorityMenu">
        <mat-icon>flag</mat-icon>
        <span>Set Priority</span>
      </button>
    </mat-menu>

    <!-- Priority Submenu -->
    <mat-menu #priorityMenu="matMenu">
      <button 
        *ngFor="let priority of priorityOptions"
        mat-menu-item 
        (click)="onSetPriority(selectedConversation!, priority.value, $event)"
      >
        <div class="priority-indicator" [style.background-color]="priority.color"></div>
        {{ priority.label }}
      </button>
    </mat-menu>
  </div>

  <!-- No Conversations State -->
  <ng-template #noConversations>
    <div class="no-conversations">
      <div class="no-conversations-icon">
        <mat-icon>chat_bubble_outline</mat-icon>
      </div>
      <h4>No conversations found</h4>
      <p *ngIf="searchQuery">
        No conversations match your search for "{{ searchQuery }}"
      </p>
      <p *ngIf="!searchQuery && hasActiveFilters()">
        No conversations match your current filters
      </p>
      <p *ngIf="!searchQuery && !hasActiveFilters()">
        Start a new conversation or check your connection
      </p>
      <button 
        mat-button 
        class="clear-search-btn"
        *ngIf="searchQuery || hasActiveFilters()"
        (click)="clearFilters()"
      >
        Clear filters
      </button>
    </div>
  </ng-template>
</div>
