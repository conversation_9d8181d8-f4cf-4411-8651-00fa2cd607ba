// Chat List Component - Futuristic Dark Theme
.chat-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: transparent;
  padding: 1rem;

  // Enhanced styling for all inputs and selects
  ::ng-deep {
    .mat-mdc-form-field {
      width: 100%;
      margin-bottom: 0.8rem;

      .mat-mdc-text-field-wrapper {
        border-radius: 12px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        backdrop-filter: blur(10px);
        transition: all 200ms ease !important;
      }

      .mdc-text-field--outlined .mdc-notched-outline {
        border-color: rgba(255, 255, 255, 0.15) !important;
        border-radius: 12px !important;
      }

      .mdc-text-field--focused .mdc-notched-outline {
        border-color: rgba(33, 150, 243, 0.3) !important;
        box-shadow: 0 0 8px rgba(33, 150, 243, 0.2) !important;
      }

      input, textarea, .mat-mdc-select-value {
        color: #ffffff !important;
        padding: 1rem !important;
        font-size: 0.9rem !important;
      }

      .mat-mdc-form-field-infix {
        padding: 1rem !important;
        min-height: auto !important;
      }

      .mat-mdc-form-field-label {
        color: rgba(255, 255, 255, 0.7) !important;
      }

      .mat-mdc-form-field-icon-suffix {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }
  }
}

// Search and Sort Section
.search-sort-section {
  padding: 0;
  margin-bottom: 0.8rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  padding: 1rem;

  .search-field {
    width: 100%;
    margin-bottom: 0.8rem;
  }

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 0.8rem;

    .sort-select {
      flex: 1;
    }

    .sort-direction-btn {
      min-width: 44px;
      min-height: 44px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 200ms ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
      }

      mat-icon {
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }
    }
  }
}

// Filter Header
.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  margin-bottom: 0.8rem;

  .filter-toggle {
    position: relative;
    min-width: 40px;
    min-height: 40px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s ease;

    &:hover {
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.05);
    }

    &.active {
      color: #ffffff;
      background: rgba(255, 255, 255, 0.1);
    }

    .filter-count {
      position: absolute;
      top: 4px;
      right: 4px;
      background: #ff4444;
      color: #ffffff;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
    }
  }

  .active-filters {
    .clear-filters-btn {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.7);
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 0.3rem 0.6rem;
      min-height: 32px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      }

      mat-icon {
        font-size: 0.9rem;
        width: 0.9rem;
        height: 0.9rem;
        margin-right: 0.3rem;
      }
    }
  }
}

// Filters Panel
.filters-panel {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  margin-bottom: 0.8rem;
  animation: slideDown 0.2s ease-out;

  .filter-group {
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);

    &:last-child {
      margin-bottom: 0;
    }

    .filter-label {
      display: block;
      font-size: 0.8rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.8rem;
    }

    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 0.8rem;

      .filter-chip {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.8rem 1rem;
        font-size: 0.8rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: rgba(255, 255, 255, 0.8);
        transition: all 200ms ease;
        min-height: 44px;
        cursor: pointer;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
        }

        &.active {
          background: rgba(33, 150, 243, 0.2);
          border-color: rgba(33, 150, 243, 0.4);
          color: rgba(255, 255, 255, 0.95);
        }

        .status-indicator,
        .priority-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Conversations List
.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  margin-top: 0.8rem;

  .conversation-item {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    padding: 0.8rem 1rem;
    margin-bottom: 0.8rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 200ms ease;
    position: relative;
    backdrop-filter: blur(10px);
    min-height: 80px;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
    }

    &.selected {
      background: rgba(255, 255, 255, 0.08);
      border-left: 3px solid rgba(255, 255, 255, 0.6);
    }

    &.unread {
      background: rgba(255, 255, 255, 0.02);
      border-left: 3px solid #4caf50;

      .customer-name {
        font-weight: 600;
      }
    }

    .conversation-avatar {
      position: relative;
      flex-shrink: 0;

      .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          font-size: 1.1rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .platform-indicator {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 18px;
        height: 18px;
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 0.8rem;
          width: 0.8rem;
          height: 0.8rem;
        }
      }
    }

    .conversation-details {
      flex: 1;
      min-width: 0;

      .conversation-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 0.3rem;

        .customer-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          min-width: 0;

          .customer-name {
            font-size: 0.9rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.95);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .status-priority {
            display: flex;
            gap: 0.3rem;

            .status-badge,
            .priority-badge {
              width: 8px;
              height: 8px;
              border-radius: 50%;
            }
          }
        }

        .conversation-meta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.2rem;
          flex-shrink: 0;

          .last-message-time {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.6);
            white-space: nowrap;
          }

          .unread-count {
            background: #4caf50;
            color: #ffffff;
            border-radius: 10px;
            padding: 0.1rem 0.4rem;
            font-size: 0.7rem;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
            line-height: 1.2;
          }
        }
      }

      .conversation-preview {
        margin-bottom: 0.3rem;

        .subject {
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.8);
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.3;
        }

        .tags {
          display: flex;
          gap: 0.3rem;
          margin-top: 0.3rem;
          flex-wrap: wrap;

          .tag {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            padding: 0.1rem 0.4rem;
            border-radius: 8px;
            font-size: 0.7rem;
            white-space: nowrap;
          }

          .tag-more {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.6);
            padding: 0.1rem 0.4rem;
            border-radius: 8px;
            font-size: 0.7rem;
          }
        }
      }

      .ai-summary {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        padding: 0.3rem 0.5rem;
        background: rgba(156, 39, 176, 0.1);
        border: 1px solid rgba(156, 39, 176, 0.2);
        border-radius: 6px;
        margin-top: 0.3rem;

        .ai-icon {
          font-size: 0.9rem;
          width: 0.9rem;
          height: 0.9rem;
          color: #ab47bc;
        }

        .summary-text {
          font-size: 0.75rem;
          color: rgba(255, 255, 255, 0.8);
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }

    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
      opacity: 0;
      transition: all 200ms ease;

      .quick-action-btn {
        min-width: 44px;
        min-height: 44px;
        color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        backdrop-filter: blur(10px);
        transition: all 200ms ease;

        &:hover {
          color: rgba(255, 255, 255, 0.9);
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(-1px);
        }

        &.priority-urgent {
          color: #f44336;
          background: rgba(244, 67, 54, 0.1);

          &:hover {
            background: rgba(244, 67, 54, 0.2);
          }
        }

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }

    &:hover .quick-actions {
      opacity: 1;
    }
  }
}

// No Conversations State
.no-conversations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  flex: 1;

  .no-conversations-icon {
    margin-bottom: 1rem;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      opacity: 0.5;
    }
  }

  h4 {
    font-size: 1.1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    max-width: 250px;
  }

  .clear-search-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

// Context Menu Styling
::ng-deep .conversation-context-menu {
  .mat-mdc-menu-panel {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }

  .mat-mdc-menu-item {
    color: rgba(255, 255, 255, 0.9);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .priority-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
  }
}

// Mobile Responsive Design
@media (max-width: 768px) {
  .conversation-item {
    padding: 1rem 0.8rem;
    min-height: 88px; // Larger touch target

    .conversation-avatar .customer-avatar {
      width: 44px;
      height: 44px;
    }

    .quick-actions {
      opacity: 1; // Always visible on mobile

      .quick-action-btn {
        min-width: 44px;
        min-height: 44px;
      }
    }
  }

  .filter-toggle {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  .filter-chip {
    min-height: 36px !important;
    padding: 0.4rem 0.8rem !important;
  }
}

// Performance optimizations
.conversations-list,
.conversation-item,
.quick-actions {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .conversation-item,
  .quick-actions,
  .filters-panel {
    transition: none !important;
    animation: none !important;
  }
}
