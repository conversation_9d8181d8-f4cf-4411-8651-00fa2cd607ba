import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Subject } from 'rxjs';

import {
  Conversation,
  ConversationFilter,
  ConversationSort,
  CommunicationProvider
} from '../../models/conversation.models';

@Component({
  selector: 'app-chat-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatBadgeModule,
    MatTooltipModule,
    MatMenuModule,
    MatDividerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './chat-list.component.html',
  styleUrls: ['./chat-list.component.scss']
})
export class ChatListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  @Input() conversations: Conversation[] = [];
  @Input() selectedConversation: Conversation | null = null;

  @Output() conversationSelected = new EventEmitter<Conversation>();
  @Output() filtersChanged = new EventEmitter<ConversationFilter>();

  // Search and Sort state
  searchQuery: string = '';
  sortConfig: ConversationSort = { field: 'lastMessageAt', direction: 'desc' };

  // Filter state
  activeFilters: ConversationFilter = {};
  showFilters = false;

  // Status and priority options
  statusOptions = [
    { value: 'active', label: 'Active', color: '#4caf50' },
    { value: 'pending', label: 'Pending', color: '#ff9800' },
    { value: 'resolved', label: 'Resolved', color: '#2196f3' },
    { value: 'archived', label: 'Archived', color: '#9e9e9e' }
  ];

  priorityOptions = [
    { value: 'urgent', label: 'Urgent', color: '#f44336' },
    { value: 'high', label: 'High', color: '#ff5722' },
    { value: 'medium', label: 'Medium', color: '#ff9800' },
    { value: 'low', label: 'Low', color: '#4caf50' }
  ];

  constructor() {}

  ngOnInit(): void {
    // Initialize component
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

 

  isSelected(conversation: Conversation): boolean {
    return this.selectedConversation?.id === conversation.id;
  }

  getStatusColor(status: string): string {
    const statusOption = this.statusOptions.find(opt => opt.value === status);
    return statusOption?.color || '#9e9e9e';
  }

  getPriorityColor(priority: string): string {
    const priorityOption = this.priorityOptions.find(opt => opt.value === priority);
    return priorityOption?.color || '#9e9e9e';
  }

  getPlatformIcon(platform: CommunicationProvider): string {
    const iconMap: Record<CommunicationProvider, string> = {
      email: 'email',
      whatsapp: 'chat',
      facebook: 'facebook',
      instagram: 'camera_alt',
      tiktok: 'music_note',
      discord: 'forum',
      slack: 'work',
      templates: 'description'
    };
    return iconMap[platform] || 'chat';
  }

  getPlatformColor(platform: CommunicationProvider): string {
    const colorMap: Record<CommunicationProvider, string> = {
      email: '#4285f4',
      whatsapp: '#25d366',
      facebook: '#1877f2',
      instagram: '#e4405f',
      tiktok: '#ff0050',
      discord: '#5865f2',
      slack: '#4a154b',
      templates: '#6c757d'
    };
    return colorMap[platform] || '#9e9e9e';
  }

  formatLastMessageTime(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return date.toLocaleDateString();
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  

  

  hasActiveFilters(): boolean {
    return Object.keys(this.activeFilters).some(key => {
      const value = this.activeFilters[key as keyof ConversationFilter];
      return Array.isArray(value) ? value.length > 0 : !!value;
    });
  }

  getActiveFilterCount(): number {
    let count = 0;
    if (this.activeFilters.status?.length) count++;
    if (this.activeFilters.priority?.length) count++;
    if (this.activeFilters.platform?.length) count++;
    if (this.activeFilters.assignedAgent?.length) count++;
    if (this.activeFilters.tags?.length) count++;
    return count;
  }

  trackByConversationId(_index: number, conversation: Conversation): string {
    return conversation.id;
  }

  // Search functionality
  onSearchChanged(query: string): void {
    this.searchQuery = query;
    this.emitFiltersChanged();
  }

  // Sort functionality
  onSortChanged(sort: ConversationSort): void {
    this.sortConfig = sort;
    this.emitFiltersChanged();
  }

  // Clear search
  clearSearch(): void {
    this.searchQuery = '';
    this.emitFiltersChanged();
  }

  // Emit filters changed
  private emitFiltersChanged(): void {
    const filters: ConversationFilter = {
      ...this.activeFilters,
      searchQuery: this.searchQuery
    };
    this.filtersChanged.emit(filters);
  }

  // Clear all filters
  clearFilters(): void {
    this.searchQuery = '';
    this.activeFilters = {};
    this.emitFiltersChanged();
  }

  // Handle conversation click
  onConversationClick(conversation: Conversation): void {
    this.conversationSelected.emit(conversation);
  }

  // Context menu actions
  onMarkAsRead(conversation: Conversation, event: Event): void {
    event.stopPropagation();
    // Implement mark as read functionality
    console.log('Mark as read:', conversation.id);
  }

  onArchiveConversation(conversation: Conversation, event: Event): void {
    event.stopPropagation();
    // Implement archive functionality
    console.log('Archive conversation:', conversation.id);
  }

  onAssignAgent(conversation: Conversation, event: Event): void {
    event.stopPropagation();
    // Implement assign agent functionality
    console.log('Assign agent:', conversation.id);
  }

  onSetPriority(conversation: Conversation, priority: string, event: Event): void {
    event.stopPropagation();
    // Implement set priority functionality
    console.log('Set priority:', conversation.id, priority);
  }

  toggleStatusFilter(status: string): void {
    if (!this.activeFilters.status) {
      this.activeFilters.status = [];
    }

    const index = this.activeFilters.status.indexOf(status);
    if (index > -1) {
      this.activeFilters.status.splice(index, 1);
    } else {
      this.activeFilters.status.push(status);
    }

  }

  togglePriorityFilter(priority: string): void {
    if (!this.activeFilters.priority) {
      this.activeFilters.priority = [];
    }

    const index = this.activeFilters.priority.indexOf(priority);
    if (index > -1) {
      this.activeFilters.priority.splice(index, 1);
    } else {
      this.activeFilters.priority.push(priority);
    }

  }
}
