// Chat Interface Component - Futuristic Dark Theme
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: transparent;
  position: relative;

  &.no-conversation {
    justify-content: center;
    align-items: center;
  }
}

// No Conversation Selected State
.no-conversation-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .no-conversation-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    max-width: 300px;

    .no-conversation-icon {
      margin-bottom: 1.5rem;

      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        opacity: 0.5;
      }
    }

    h3 {
      font-size: 1.3rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.8rem;
    }

    p {
      font-size: 0.95rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
    }

    .provider-info {
      .provider-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.8);

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }

        .connection-status {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.connected {
            background: #4caf50;
            box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
          }

          &.disconnected {
            background: #f44336;
            box-shadow: 0 0 4px rgba(244, 67, 54, 0.3);
          }
        }
      }
    }
  }
}

// Active Conversation Interface
.conversation-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Conversation Header
.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);

  .customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    .customer-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-placeholder {
        font-size: 1.1rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .customer-details {
      .customer-name {
        font-size: 1rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        margin: 0 0 0.2rem 0;
      }

      .conversation-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.6);

        .conversation-status {
          text-transform: capitalize;
        }

        .separator {
          opacity: 0.5;
        }

        .conversation-priority {
          text-transform: capitalize;
        }
      }
    }
  }

  .conversation-actions {
    display: flex;
    gap: 0.3rem;

    .action-btn {
      min-width: 40px;
      min-height: 40px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      mat-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
    }
  }
}

// Messages Container
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  position: relative;
  scroll-behavior: smooth;

  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.drag-over {
    background: rgba(33, 150, 243, 0.1);
    border: 2px dashed rgba(33, 150, 243, 0.5);
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Messages Loading
.messages-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);

  span {
    font-size: 0.9rem;
  }
}

// Messages List
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Message Wrapper
.message-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.8rem;
  max-width: 80%;

  &.own-message {
    align-self: flex-end;
    flex-direction: row-reverse;

    .message-bubble {
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.8) 0%, rgba(21, 101, 192, 0.9) 100%);
      border: 1px solid rgba(33, 150, 243, 0.3);
    }
  }

  &.customer-message {
    align-self: flex-start;

    .message-bubble {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

// Message Bubble
.message-bubble {
  padding: 0.8rem 1rem;
  border-radius: 18px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .message-content {
    .message-text {
      font-size: 0.9rem;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.95);
      word-wrap: break-word;
    }

    .message-attachments {
      margin-top: 0.5rem;

      .attachment-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        margin-bottom: 0.3rem;

        &:last-child {
          margin-bottom: 0;
        }

        .attachment-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
          color: rgba(255, 255, 255, 0.7);
        }

        .attachment-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-width: 0;

          .attachment-name {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.9);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .attachment-size {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .download-btn {
          min-width: 28px;
          min-height: 28px;
          color: rgba(255, 255, 255, 0.7);

          &:hover {
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
          }

          mat-icon {
            font-size: 0.9rem;
            width: 0.9rem;
            height: 0.9rem;
          }
        }
      }
    }
  }

  .message-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
    gap: 0.5rem;

    .message-time {
      font-size: 0.7rem;
      color: rgba(255, 255, 255, 0.6);
    }

    .message-status {
      font-size: 0.8rem;
      width: 0.8rem;
      height: 0.8rem;
    }
  }
}

// Sender Info
.sender-info {
  flex-shrink: 0;

  .sender-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      font-size: 0.8rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

// Typing Indicator
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  max-width: 200px;

  .typing-dots {
    display: flex;
    gap: 0.2rem;

    span {
      width: 6px;
      height: 6px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: typingDot 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.4s; }
    }
  }

  .typing-text {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
  }
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// Drag and Drop Overlay
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(33, 150, 243, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .drag-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(33, 150, 243, 0.9);
    font-size: 1.1rem;
    font-weight: 500;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }
  }
}

// Message Input Area
.message-input-area {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  position: relative;
}

// File Preview
.file-preview {
  padding: 1rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);

  .file-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.8rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);

    button {
      min-width: 28px;
      min-height: 28px;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }

  .file-preview-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .file-preview-item {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      padding: 0.6rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;

      .file-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
        color: rgba(255, 255, 255, 0.7);
      }

      .file-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;

        .file-name {
          font-size: 0.85rem;
          color: rgba(255, 255, 255, 0.9);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .file-size {
          font-size: 0.75rem;
          color: rgba(255, 255, 255, 0.6);
        }
      }

      button {
        min-width: 28px;
        min-height: 28px;
        color: rgba(255, 255, 255, 0.6);

        &:hover {
          color: rgba(255, 255, 255, 0.9);
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}

// Voice Recording
.voice-recording {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: rgba(244, 67, 54, 0.1);
  border-bottom: 1px solid rgba(244, 67, 54, 0.2);

  .recording-indicator {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #f44336;
    font-size: 0.9rem;
    font-weight: 500;

    .recording-dot {
      width: 8px;
      height: 8px;
      background: #f44336;
      border-radius: 50%;
      animation: recordingPulse 1s infinite ease-in-out;
    }
  }

  .stop-recording-btn {
    min-width: 40px;
    min-height: 40px;
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border-radius: 50%;

    &:hover {
      background: rgba(244, 67, 54, 0.3);
    }
  }
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

// Input Controls
.input-controls {
  display: flex;
  align-items: flex-end;
  gap: 0.8rem;
  padding: 1rem 1.5rem;

  .input-action-btn {
    min-width: 40px;
    min-height: 40px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.05);
    }

    &.voice-btn {
      &.recording {
        background: rgba(244, 67, 54, 0.2);
        color: #f44336;

        &:hover {
          background: rgba(244, 67, 54, 0.3);
        }
      }
    }

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }

  .message-input-container {
    flex: 1;
    position: relative;

    .message-input {
      width: 100%;
      min-height: 40px;
      max-height: 120px;
      padding: 0.8rem 1rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      color: #ffffff;
      font-size: 0.9rem;
      line-height: 1.4;
      resize: none;
      outline: none;
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        border-color: rgba(33, 150, 243, 0.5);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .send-btn {
    min-width: 44px;
    min-height: 44px;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: #ffffff;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      transform: scale(1.05);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
      cursor: not-allowed;
      transform: none;
    }

    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
}

// Emoji Picker
.emoji-picker {
  position: absolute;
  bottom: 100%;
  right: 1.5rem;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  z-index: 100;
  animation: slideUp 0.2s ease-out;

  .emoji-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;

    .emoji-btn {
      width: 36px;
      height: 36px;
      border: none;
      background: transparent;
      border-radius: 6px;
      font-size: 1.2rem;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
      }
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Context Menu Styling
::ng-deep .mat-mdc-menu-panel {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);

  .mat-mdc-menu-item {
    color: rgba(255, 255, 255, 0.9);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    mat-icon {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

// Mobile Responsive Design
@media (max-width: 768px) {
  .conversation-header {
    padding: 0.8rem 1rem;

    .customer-info {
      gap: 0.8rem;

      .customer-avatar {
        width: 36px;
        height: 36px;
      }

      .customer-details {
        .customer-name {
          font-size: 0.9rem;
        }

        .conversation-meta {
          font-size: 0.75rem;
        }
      }
    }

    .conversation-actions {
      .action-btn {
        min-width: 44px;
        min-height: 44px;
      }
    }
  }

  .messages-container {
    padding: 0.8rem;
  }

  .message-wrapper {
    max-width: 90%;

    .message-bubble {
      padding: 0.6rem 0.8rem;

      .message-content .message-text {
        font-size: 0.85rem;
      }
    }
  }

  .input-controls {
    padding: 0.8rem 1rem;
    gap: 0.6rem;

    .input-action-btn {
      min-width: 44px;
      min-height: 44px;
    }

    .send-btn {
      min-width: 48px;
      min-height: 48px;
    }

    .message-input-container .message-input {
      min-height: 44px;
      padding: 0.8rem 1rem;
      font-size: 0.85rem;
    }
  }

  .file-preview {
    padding: 0.8rem 1rem 0 1rem;
  }

  .emoji-picker {
    right: 1rem;
    left: 1rem;
    width: auto;

    .emoji-grid {
      grid-template-columns: repeat(6, 1fr);

      .emoji-btn {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .conversation-header {
    padding: 0.6rem 0.8rem;
  }

  .messages-container {
    padding: 0.6rem;
  }

  .input-controls {
    padding: 0.6rem 0.8rem;
    flex-wrap: wrap;

    .message-input-container {
      order: 1;
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .input-action-btn,
    .send-btn {
      order: 2;
    }
  }

  .emoji-picker .emoji-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

// Performance optimizations
.chat-interface,
.message-bubble,
.input-controls,
.emoji-picker {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .message-bubble,
  .input-action-btn,
  .send-btn,
  .emoji-picker {
    transition: none !important;
    animation: none !important;
  }

  @keyframes typingDot {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }

  @keyframes recordingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @keyframes slideUp {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

// No Conversation Selected State
.no-conversation-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;

  .no-conversation-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    max-width: 300px;

    .no-conversation-icon {
      margin-bottom: 1.5rem;

      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        opacity: 0.5;
      }
    }

    h3 {
      font-size: 1.3rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 0.8rem;
    }

    p {
      font-size: 0.95rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
    }

    .provider-info {
      .provider-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.8);

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }

        .connection-status {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.connected {
            background: #4caf50;
            box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
          }

          &.disconnected {
            background: #f44336;
            box-shadow: 0 0 4px rgba(244, 67, 54, 0.3);
          }
        }
      }
    }
  }
}

// Active Conversation Interface
.conversation-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Conversation Header
.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);

  .customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;

    .customer-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-placeholder {
        font-size: 1.1rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .customer-details {
      .customer-name {
        font-size: 1rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        margin: 0 0 0.2rem 0;
      }

      .conversation-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.6);

        .conversation-status {
          text-transform: capitalize;
        }

        .separator {
          opacity: 0.5;
        }

        .conversation-priority {
          text-transform: capitalize;
        }
      }
    }
  }

  .conversation-actions {
    display: flex;
    gap: 0.3rem;

    .action-btn {
      min-width: 40px;
      min-height: 40px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
      }

      mat-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
    }
  }
}

// Messages Container
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  position: relative;
  scroll-behavior: smooth;

  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.drag-over {
    background: rgba(33, 150, 243, 0.1);
    border: 2px dashed rgba(33, 150, 243, 0.5);
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Messages Loading
.messages-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);

  span {
    font-size: 0.9rem;
  }
}

// Messages List
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Message Wrapper
.message-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.8rem;
  max-width: 80%;

  &.own-message {
    align-self: flex-end;
    flex-direction: row-reverse;

    .message-bubble {
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.8) 0%, rgba(21, 101, 192, 0.9) 100%);
      border: 1px solid rgba(33, 150, 243, 0.3);
    }
  }

  &.customer-message {
    align-self: flex-start;

    .message-bubble {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

// Message Bubble
.message-bubble {
  padding: 0.8rem 1rem;
  border-radius: 18px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .message-content {
    .message-text {
      font-size: 0.9rem;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.95);
      word-wrap: break-word;
    }

    .message-attachments {
      margin-top: 0.5rem;

      .attachment-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        margin-bottom: 0.3rem;

        &:last-child {
          margin-bottom: 0;
        }

        .attachment-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
          color: rgba(255, 255, 255, 0.7);
        }

        .attachment-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-width: 0;

          .attachment-name {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.9);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .attachment-size {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .download-btn {
          min-width: 28px;
          min-height: 28px;
          color: rgba(255, 255, 255, 0.7);

          &:hover {
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
          }

          mat-icon {
            font-size: 0.9rem;
            width: 0.9rem;
            height: 0.9rem;
          }
        }
      }
    }
  }

  .message-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
    gap: 0.5rem;

    .message-time {
      font-size: 0.7rem;
      color: rgba(255, 255, 255, 0.6);
    }

    .message-status {
      font-size: 0.8rem;
      width: 0.8rem;
      height: 0.8rem;
    }
  }
}

// Sender Info
.sender-info {
  flex-shrink: 0;

  .sender-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      font-size: 0.8rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

// Typing Indicator
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  max-width: 200px;

  .typing-dots {
    display: flex;
    gap: 0.2rem;

    span {
      width: 6px;
      height: 6px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: typingDot 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.4s; }
    }
  }

  .typing-text {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
  }
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// Drag and Drop Overlay
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(33, 150, 243, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .drag-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(33, 150, 243, 0.9);
    font-size: 1.1rem;
    font-weight: 500;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }
  }
}
