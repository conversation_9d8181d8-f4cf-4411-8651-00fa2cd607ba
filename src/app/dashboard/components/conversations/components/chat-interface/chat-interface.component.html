<!-- Chat Interface Component -->
<div class="chat-interface" [class.no-conversation]="!selectedConversation">
  <!-- No Conversation Selected State -->
  <div class="no-conversation-state" *ngIf="!selectedConversation">
    <div class="no-conversation-content">
      <div class="no-conversation-icon">
        <mat-icon>chat_bubble_outline</mat-icon>
      </div>
      <h3>Select a conversation</h3>
      <p>Choose a conversation from the list to start chatting with customers.</p>
      <div class="provider-info" *ngIf="providerConfig">
        <div class="provider-badge">
          <mat-icon [style.color]="providerConfig.color">{{ providerConfig.icon }}</mat-icon>
          <span>{{ providerConfig.name }}</span>
          <div 
            class="connection-status"
            [class.connected]="providerConfig.isConnected"
            [class.disconnected]="!providerConfig.isConnected"
          ></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Conversation Interface -->
  <div class="conversation-interface" *ngIf="selectedConversation">
    <!-- Conversation Header -->
    <div class="conversation-header">
      <div class="customer-info">
        <div class="customer-avatar">
          <img 
            *ngIf="selectedConversation.customer.avatar; else defaultAvatar" 
            [src]="selectedConversation.customer.avatar" 
            [alt]="selectedConversation.customer.name"
          />
          <ng-template #defaultAvatar>
            <div class="avatar-placeholder">
              {{ selectedConversation.customer.name.charAt(0).toUpperCase() }}
            </div>
          </ng-template>
        </div>
        <div class="customer-details">
          <h4 class="customer-name">{{ selectedConversation.customer.name }}</h4>
          <div class="conversation-meta">
            <span class="conversation-status">{{ selectedConversation.status }}</span>
            <span class="separator">•</span>
            <span class="conversation-priority">{{ selectedConversation.priority }} priority</span>
          </div>
        </div>
      </div>

      <div class="conversation-actions">
        <button 
          mat-icon-button 
          class="action-btn"
          matTooltip="Conversation details"
        >
          <mat-icon>info</mat-icon>
        </button>
        <button 
          mat-icon-button 
          class="action-btn"
          matTooltip="Search in conversation"
        >
          <mat-icon>search</mat-icon>
        </button>
        <button 
          mat-icon-button 
          class="action-btn"
          [matMenuTriggerFor]="conversationMenu"
          matTooltip="More options"
        >
          <mat-icon>more_vert</mat-icon>
        </button>
      </div>
    </div>

    <!-- Messages Container -->
    <div 
      class="messages-container"
      #messagesContainer
      [class.loading]="isLoading"
      (dragover)="onDragOver($event)"
      (dragleave)="onDragLeave($event)"
      (drop)="onDrop($event)"
      [class.drag-over]="dragOver"
    >
      <!-- Loading State -->
      <div class="messages-loading" *ngIf="isLoading">
        <mat-spinner diameter="32"></mat-spinner>
        <span>Loading messages...</span>
      </div>

      <!-- Messages List -->
      <div class="messages-list" *ngIf="!isLoading">
        <div 
          *ngFor="let message of messages; trackBy: trackByMessageId"
          class="message-wrapper"
          [class.own-message]="isOwnMessage(message)"
          [class.customer-message]="!isOwnMessage(message)"
          (click)="onMessageClick(message)"
        >
          <!-- Message Bubble -->
          <div class="message-bubble">
            <!-- Message Content -->
            <div class="message-content">
              <div class="message-text" *ngIf="message.messageType === 'text'">
                {{ message.content }}
              </div>
              
              <!-- File Attachments -->
              <div class="message-attachments" *ngIf="message.metadata.attachments?.length">
                <div 
                  *ngFor="let attachment of message.metadata.attachments"
                  class="attachment-item"
                >
                  <mat-icon class="attachment-icon">attach_file</mat-icon>
                  <div class="attachment-info">
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
                  </div>
                  <button mat-icon-button class="download-btn">
                    <mat-icon>download</mat-icon>
                  </button>
                </div>
              </div>
            </div>

            <!-- Message Meta -->
            <div class="message-meta">
              <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
              <mat-icon 
                class="message-status"
                [style.color]="getMessageStatusColor(message.status)"
                *ngIf="isOwnMessage(message)"
              >
                {{ getMessageStatusIcon(message.status) }}
              </mat-icon>
            </div>
          </div>

          <!-- Sender Info (for customer messages) -->
          <div class="sender-info" *ngIf="!isOwnMessage(message)">
            <div class="sender-avatar">
              <img 
                *ngIf="selectedConversation.customer.avatar; else senderDefaultAvatar" 
                [src]="selectedConversation.customer.avatar" 
                [alt]="selectedConversation.customer.name"
              />
              <ng-template #senderDefaultAvatar>
                <div class="avatar-placeholder">
                  {{ selectedConversation.customer.name.charAt(0).toUpperCase() }}
                </div>
              </ng-template>
            </div>
          </div>
        </div>

        <!-- Typing Indicators -->
        <div class="typing-indicator" *ngIf="typingIndicators.length > 0">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span class="typing-text">{{ selectedConversation.customer.name }} is typing...</span>
        </div>
      </div>

      <!-- Drag and Drop Overlay -->
      <div class="drag-overlay" *ngIf="dragOver">
        <div class="drag-content">
          <mat-icon>cloud_upload</mat-icon>
          <span>Drop files here to attach</span>
        </div>
      </div>
    </div>

    <!-- Message Input Area -->
    <div class="message-input-area">
      <!-- File Preview -->
      <div class="file-preview" *ngIf="selectedFiles.length > 0">
        <div class="file-preview-header">
          <span>{{ selectedFiles.length }} file(s) selected</span>
          <button mat-icon-button (click)="selectedFiles = []">
            <mat-icon>clear</mat-icon>
          </button>
        </div>
        <div class="file-preview-list">
          <div 
            *ngFor="let file of selectedFiles; let i = index"
            class="file-preview-item"
          >
            <mat-icon class="file-icon">attach_file</mat-icon>
            <div class="file-info">
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
            <button mat-icon-button (click)="removeFile(i)">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Voice Recording Indicator -->
      <div class="voice-recording" *ngIf="isRecording">
        <div class="recording-indicator">
          <div class="recording-dot"></div>
          <span>Recording {{ formatRecordingTime(recordingTime) }}</span>
        </div>
        <button mat-icon-button class="stop-recording-btn" (click)="stopVoiceRecording()">
          <mat-icon>stop</mat-icon>
        </button>
      </div>

      <!-- Input Controls -->
      <div class="input-controls">
        <!-- Attachment Button -->
        <button 
          mat-icon-button 
          class="input-action-btn"
          [matMenuTriggerFor]="attachmentMenu"
          matTooltip="Attach files"
        >
          <mat-icon>attach_file</mat-icon>
        </button>

        <!-- Message Input -->
        <div class="message-input-container">
          <textarea
            #messageInput
            class="message-input"
            [(ngModel)]="newMessage"
            (keydown)="onKeyDown($event)"
            placeholder="Type your message..."
            rows="1"
            [disabled]="isRecording"
          ></textarea>
        </div>

        <!-- Emoji Button -->
        <button 
          mat-icon-button 
          class="input-action-btn"
          (click)="toggleEmojiPicker()"
          matTooltip="Add emoji"
        >
          <mat-icon>sentiment_satisfied</mat-icon>
        </button>

        <!-- Voice Message Button -->
        <button 
          mat-icon-button 
          class="input-action-btn voice-btn"
          [class.recording]="isRecording"
          (click)="isRecording ? stopVoiceRecording() : startVoiceRecording()"
          *ngIf="canUseFeature('voice_messages')"
          [matTooltip]="isRecording ? 'Stop recording' : 'Record voice message'"
        >
          <mat-icon>{{ isRecording ? 'stop' : 'mic' }}</mat-icon>
        </button>

        <!-- Send Button -->
        <button 
          mat-icon-button 
          class="send-btn"
          (click)="onSendMessage()"
          [disabled]="!newMessage.trim() && selectedFiles.length === 0"
          matTooltip="Send message"
        >
          <mat-icon>send</mat-icon>
        </button>
      </div>

      <!-- Emoji Picker -->
      <div class="emoji-picker" *ngIf="showEmojiPicker">
        <div class="emoji-grid">
          <button 
            *ngFor="let emoji of ['😀', '😊', '😍', '🤔', '😢', '😡', '👍', '👎', '❤️', '🎉']"
            class="emoji-btn"
            (click)="addEmoji(emoji)"
          >
            {{ emoji }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Context Menus -->
  <mat-menu #conversationMenu="matMenu">
    <button mat-menu-item>
      <mat-icon>archive</mat-icon>
      <span>Archive conversation</span>
    </button>
    <button mat-menu-item>
      <mat-icon>person_add</mat-icon>
      <span>Assign to agent</span>
    </button>
    <button mat-menu-item>
      <mat-icon>flag</mat-icon>
      <span>Set priority</span>
    </button>
  </mat-menu>

  <mat-menu #attachmentMenu="matMenu">
    <button mat-menu-item (click)="fileInput.click()">
      <mat-icon>attach_file</mat-icon>
      <span>Upload file</span>
    </button>
    <button mat-menu-item *ngIf="canUseFeature('media_sharing')">
      <mat-icon>image</mat-icon>
      <span>Upload image</span>
    </button>
  </mat-menu>

  <!-- Hidden File Input -->
  <input 
    #fileInput
    type="file"
    multiple
    style="display: none"
    (change)="onFileSelected($event)"
  />
</div>
