import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject } from 'rxjs';

import { 
  Conversation, 
  Message, 
  CommunicationProvider, 
  ProviderConfig,
  TypingIndicator 
} from '../../models/conversation.models';

@Component({
  selector: 'app-chat-interface',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatMenuModule,
    MatTooltipModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './chat-interface.component.html',
  styleUrls: ['./chat-interface.component.scss']
})
export class ChatInterfaceComponent implements OnInit, OnDestroy, OnChanges {
  private destroy$ = new Subject<void>();

  @ViewChild('messageInput') messageInput!: ElementRef<HTMLTextAreaElement>;
  @ViewChild('messagesContainer') messagesContainer!: ElementRef<HTMLDivElement>;

  @Input() selectedConversation: Conversation | null = null;
  @Input() activeProvider: CommunicationProvider = 'email';
  @Input() providerConfig: ProviderConfig | null = null;
  @Input() isLoading = false;

  @Output() messageSelected = new EventEmitter<Message>();
  @Output() providerChanged = new EventEmitter<CommunicationProvider>();

  // Chat state
  messages: Message[] = [];
  newMessage = '';
  isTyping = false;
  typingIndicators: TypingIndicator[] = [];
  
  // UI state
  showEmojiPicker = false;
  showAttachmentMenu = false;
  isRecording = false;
  recordingTime = 0;
  
  // File upload
  selectedFiles: File[] = [];
  dragOver = false;

  constructor() {}

  ngOnInit(): void {
    this.loadMessages();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(): void {
    if (this.selectedConversation) {
      this.loadMessages();
    }
  }

  private loadMessages(): void {
    if (!this.selectedConversation) {
      this.messages = [];
      return;
    }

    // Simulate loading messages
    this.isLoading = true;
    setTimeout(() => {
      this.messages = this.generateMockMessages();
      this.isLoading = false;
      this.scrollToBottom();
    }, 500);
  }

  private generateMockMessages(): Message[] {
    if (!this.selectedConversation) return [];

    return [
      {
        id: '1',
        conversationId: this.selectedConversation.id,
        senderId: this.selectedConversation.customer.id,
        senderType: 'customer',
        content: 'Hello, I need help with my recent order.',
        messageType: 'text',
        timestamp: new Date(Date.now() - 3600000),
        status: 'read',
        metadata: {
          platform: this.activeProvider
        }
      },
      {
        id: '2',
        conversationId: this.selectedConversation.id,
        senderId: 'agent-1',
        senderType: 'agent',
        content: 'Hi! I\'d be happy to help you with your order. Could you please provide your order number?',
        messageType: 'text',
        timestamp: new Date(Date.now() - 3500000),
        status: 'read',
        metadata: {
          platform: this.activeProvider
        }
      }
    ];
  }

  onSendMessage(): void {
    if (!this.newMessage.trim() && this.selectedFiles.length === 0) return;
    if (!this.selectedConversation) return;

    const message: Message = {
      id: Date.now().toString(),
      conversationId: this.selectedConversation.id,
      senderId: 'current-agent',
      senderType: 'agent',
      content: this.newMessage.trim(),
      messageType: this.selectedFiles.length > 0 ? 'file' : 'text',
      timestamp: new Date(),
      status: 'sent',
      metadata: {
        platform: this.activeProvider,
        attachments: this.selectedFiles.map(file => ({
          id: Date.now().toString(),
          name: file.name,
          type: file.type,
          size: file.size,
          url: URL.createObjectURL(file)
        }))
      }
    };

    this.messages.push(message);
    this.newMessage = '';
    this.selectedFiles = [];
    this.scrollToBottom();

    // Simulate message status updates
    setTimeout(() => {
      message.status = 'delivered';
    }, 1000);

    setTimeout(() => {
      message.status = 'read';
    }, 2000);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSendMessage();
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.selectedFiles = Array.from(input.files);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
    
    if (event.dataTransfer?.files) {
      this.selectedFiles = Array.from(event.dataTransfer.files);
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatMessageTime(date: Date): string {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getMessageStatusIcon(status: string): string {
    switch (status) {
      case 'sent': return 'check';
      case 'delivered': return 'done_all';
      case 'read': return 'done_all';
      case 'failed': return 'error';
      default: return 'schedule';
    }
  }

  getMessageStatusColor(status: string): string {
    switch (status) {
      case 'sent': return '#9e9e9e';
      case 'delivered': return '#2196f3';
      case 'read': return '#4caf50';
      case 'failed': return '#f44336';
      default: return '#9e9e9e';
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  onMessageClick(message: Message): void {
    this.messageSelected.emit(message);
  }

  isOwnMessage(message: Message): boolean {
    return message.senderType === 'agent' && message.senderId === 'current-agent';
  }

  trackByMessageId(_index: number, message: Message): string {
    return message.id;
  }

  // Provider-specific features
  canUseFeature(feature: string): boolean {
    return this.providerConfig?.features.some(f => f.id === feature && f.enabled) || false;
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
  }

  addEmoji(emoji: string): void {
    this.newMessage += emoji;
    this.showEmojiPicker = false;
    this.messageInput?.nativeElement.focus();
  }

  startVoiceRecording(): void {
    if (!this.canUseFeature('voice_messages')) return;
    
    this.isRecording = true;
    this.recordingTime = 0;
    
    // Simulate recording timer
    const timer = setInterval(() => {
      this.recordingTime++;
      if (this.recordingTime >= 60) { // Max 60 seconds
        this.stopVoiceRecording();
        clearInterval(timer);
      }
    }, 1000);
  }

  stopVoiceRecording(): void {
    this.isRecording = false;
    // Handle voice message creation
    console.log('Voice recording stopped:', this.recordingTime, 'seconds');
  }

  formatRecordingTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }
}
