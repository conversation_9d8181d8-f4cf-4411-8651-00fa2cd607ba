// Dashboard Header Component Styles
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.header-info {
  flex: 1;
}

.dashboard-title {
  font-size: 1.8rem;
  font-weight: 300;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  letter-spacing: 0.02em;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

.dashboard-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

.header-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.last-updated {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  backdrop-filter: blur(10px);
  
  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
    
    &.spinning {
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Mobile responsive
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .refresh-btn {
    padding: 0.6rem 1rem;
    min-height: 44px; // Touch-friendly
  }
}
