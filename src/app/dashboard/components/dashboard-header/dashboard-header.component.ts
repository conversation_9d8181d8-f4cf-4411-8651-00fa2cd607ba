import { Component, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-dashboard-header',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div class="dashboard-header">
      <div class="header-info">
        <h2 class="dashboard-title">Performance Overview</h2>
        <p class="dashboard-subtitle">Real-time insights and analytics</p>
      </div>
      <div class="header-controls">
        <span class="last-updated">Last updated: {{ getCurrentTime() }}</span>
        <button class="refresh-btn" (click)="onRefresh()" [disabled]="isRefreshing">
          <mat-icon [class.spinning]="isRefreshing">refresh</mat-icon>
          Refresh
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./dashboard-header.component.scss']
})
export class DashboardHeaderComponent {
  @Input() isRefreshing: boolean = false;
  @Output() refresh = new EventEmitter<void>();

  getCurrentTime(): string {
    return new Date().toLocaleTimeString();
  }

  onRefresh(): void {
    this.refresh.emit();
  }
}
