// Dashboard Component - Futuristic Dark Theme with Performance Optimization
.dashboard-container {
  width: 100%;
  min-height: 100vh;
  background: #000000;
  color: #ffffff;
  display: flex;
  flex-direction: column;
}

.dashboard-layout {
  display: flex;
  flex: 1;
  min-height: 100vh; // Full height since header is removed
  position: relative;
}

// Sidebar Navigation
.sidebar {
  width: 280px;
  background: rgba(0, 0, 0, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 5;
  
  &.collapsed {
    width: 70px;
    
    .sidebar-title {
      opacity: 0;
      pointer-events: none;
    }
    
    .nav-text {
      opacity: 0;
      pointer-events: none;
    }
    
    .sidebar-footer {
      opacity: 0;
      pointer-events: none;
    }
  }
}

// Sidebar Header
.sidebar-header {
  padding: 1rem 0.8rem; // Reduced padding for compact design
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: flex-start; // Align logo to left since toggle moved
  min-height: 60px; // Slightly increased for logo
}

// Logo section in sidebar
.logo-section {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  flex: 1;
}

.logo-wrapper {
  position: relative;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 4s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.sidebar-logo {
  width: 32px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 10px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 20px rgba(255, 255, 255, 0.2));
  transition: filter 0.3s ease;

  &:hover {
    filter:
      brightness(0)
      invert(1)
      drop-shadow(0 0 15px rgba(255, 255, 255, 0.6))
      drop-shadow(0 0 30px rgba(255, 255, 255, 0.3));
  }
}

// Sidebar Toggle Container (moved to bottom)
.sidebar-toggle-container {
  padding: 0.8rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar-toggle {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 44px; // Touch-friendly
  min-height: 44px; // Touch-friendly
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.3);
    outline-offset: 2px;
  }
}

.toggle-icon {
  font-size: 0.9rem;
  line-height: 1;
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: 300;
  margin: 0;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 0.05em;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  transition: opacity 0.2s ease;
  white-space: nowrap;
}

// Navigation
.sidebar-nav {
  flex: 1;
  padding: 0.5rem 0; // Reduced padding
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0.2rem 0; // Slightly increased for better visual separation
  position: relative;

  &.active {
    .nav-link {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: #ffffff;
    }

    .nav-indicator {
      opacity: 1;
    }

    .nav-icon {
      color: #ffffff;
      filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6));
    }
  }
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.8rem; // Standardized gap
  padding: 0.6rem 0.8rem; // Standardized padding
  margin: 0 0.4rem; // Standardized margin
  border-radius: 8px; // Consistent radius
  background: transparent;
  border: 1px solid transparent;
  color: rgba(255, 255, 255, 0.7); // Default opacity
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); // Smooth transition
  position: relative;
  min-height: 44px; // Touch-friendly height
  width: calc(100% - 0.8rem);
  box-sizing: border-box;
  font-weight: 500; // Standardized font weight

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.95); // Hover opacity
    transform: translateY(-1px); // Subtle lift effect
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.3);
    outline-offset: 2px;
  }

  &:active {
    transform: translateY(0); // Reset on click
  }
}

// Material Icon styling for navigation
.nav-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  min-width: 18px;
  color: rgba(255, 255, 255, 0.8) !important;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3)); // Futuristic glow effect
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex !important; // Ensure visibility
  align-items: center;
  justify-content: center;
}

// Enhanced hover and active states
.nav-link:hover {
  .nav-icon {
    color: rgba(255, 255, 255, 0.95) !important;
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.5));
  }
}

.nav-item.active {
  .nav-icon {
    color: #ffffff !important;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.7));
  }
}

.nav-text {
  font-size: 0.85rem; // Slightly smaller for compact design
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  color: inherit; // Inherit color from parent
  opacity: 1 !important; // Force visibility
  display: block !important; // Ensure it's displayed
  flex: 1; // Take available space
  text-align: left; // Align text to left
}

.nav-indicator {
  position: absolute;
  right: 0.3rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 18px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%);
  border-radius: 2px;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 2;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);

  // Ensure it's visible when active
  .nav-item.active & {
    opacity: 1 !important;
  }
}

// Sidebar Footer
.sidebar-footer {
  padding: 0.8rem; // Reduced padding for compact design
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  transition: opacity 0.2s ease;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.user-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.user-role {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

// Main Content
.main-content {
  flex: 1;
  padding: 1.5rem 2rem 1.5rem 2rem; // Reduced padding for more usable space
  overflow-y: auto;
  background: #000000;
}

.content-header {
  margin-bottom: 1.5rem; // Reduced margin for better density
  padding-bottom: 0.8rem; // Reduced padding
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.page-title {
  font-size: 1.8rem; // Slightly smaller for compact design
  font-weight: 300;
  margin: 0 0 0.4rem 0; // Reduced margin
  color: #ffffff;
  letter-spacing: 0.02em;
}

.page-description {
  font-size: 0.95rem; // Slightly smaller
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4; // Tighter line height
}

// Content Body
.content-body {
  min-height: 400px;
}

.tab-content {
  animation: fadeIn 0.3s ease-out;

  &.conversations-tab {
    padding: 0; // Remove padding for full-width conversations dashboard
    height: calc(100vh - 80px); // Adjust height for conversations layout
    overflow: hidden; // Let conversations dashboard handle its own scrolling
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

// Dashboard specific content
.welcome-message {
  margin-bottom: 2rem;
  
  h2 {
    font-size: 1.5rem;
    font-weight: 400;
    margin: 0 0 0.5rem 0;
    color: #ffffff;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

// Placeholder content
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  gap: 1rem;
}

.placeholder-icon {
  margin-bottom: 1.5rem;

  lucide-icon {
    color: rgba(255, 255, 255, 0.6);
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
  }
}

// Logo glow animation
@keyframes logoGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.coming-soon {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-top: 1rem;
}

// Mobile Responsive Design
@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    max-height: 60px;
    overflow: hidden;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);

    &.collapsed {
      width: 100%;
      max-height: 60px;
    }

    &:not(.collapsed) {
      max-height: 400px;
    }
  }

  .sidebar-header {
    padding: 0.8rem; // Reduced for mobile compact design
    min-height: 50px; // Reduced height
    flex-direction: row; // Keep horizontal layout
    justify-content: flex-start; // Logo aligned left
  }

  .sidebar-toggle-container {
    padding: 0.6rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }

  .sidebar-toggle {
    min-width: 48px; // Larger touch target on mobile
    min-height: 48px;
    padding: 1rem;
  }

  .logo-section {
    gap: 0.6rem; // Reduced gap for mobile
  }

  .sidebar-logo {
    width: 28px; // Slightly smaller on mobile
  }

  .sidebar-title {
    font-size: 1rem; // Smaller title on mobile
  }

  .sidebar-nav {
    padding: 0.3rem 0; // Further reduced for mobile
  }

  .nav-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    padding: 0 1rem;
  }

  .nav-link {
    flex-direction: column;
    gap: 0.4rem; // Reduced gap
    padding: 0.8rem 0.4rem; // Reduced padding
    margin: 0;
    text-align: center;
    min-height: 50px; // Reduced height
  }

  .nav-text {
    font-size: 0.8rem;
    opacity: 1 !important;
  }

  .nav-indicator {
    display: none;
  }

  .sidebar-footer {
    display: none;
  }

  .main-content {
    padding: 1rem 0.8rem; // Further reduced for mobile
  }

  .page-title {
    font-size: 1.4rem; // Smaller for mobile
  }

  .quick-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0.8rem 0.6rem; // Very compact for small screens
  }

  .page-title {
    font-size: 1.2rem; // Smaller for small screens
  }

  .nav-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-link {
    min-height: 50px;
    padding: 0.8rem 0.3rem;
  }

  .nav-icon-container {
    width: 18px;
    height: 18px;
  }

  .nav-icon {
    width: 16px !important;
    height: 16px !important;
    min-width: 16px;

    svg {
      width: 16px !important;
      height: 16px !important;
    }
  }

  .nav-text {
    font-size: 0.75rem;
  }
}

// Performance optimizations
.sidebar, .main-content, .nav-link, .stat-card {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .nav-link,
  .stat-card,
  .tab-content {
    transition: none !important;
    animation: none !important;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
