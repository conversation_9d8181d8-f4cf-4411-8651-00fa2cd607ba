import { Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

// Import dashboard components
import { DashboardHeaderComponent } from './components/dashboard-header/dashboard-header.component';
import { KpiGridComponent } from './components/kpi-grid/kpi-grid.component';
import { ActionBarComponent } from './components/action-bar/action-bar.component';
import { TemplatesSectionComponent, TemplateCategory, KpiTemplate } from './components/templates-section/templates-section.component';
import { KpiCard } from './components/kpi-card/kpi-card.component';
import { KpiBuilderComponent } from './components/kpi-builder/kpi-builder.component';
import { EditLayoutComponent } from './components/edit-layout/edit-layout.component';
import { ConversationsDashboardComponent } from './components/conversations/conversations-dashboard.component';

export interface DashboardTab {
  id: string;
  label: string;
  iconName: string;
  route: string;
  description: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    DashboardHeaderComponent,
    KpiGridComponent,
    ActionBarComponent,
    TemplatesSectionComponent,
    KpiBuilderComponent,
    EditLayoutComponent,
    ConversationsDashboardComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  activeTab: string = 'dashboard';
  sidebarCollapsed: boolean = false;

  // Dashboard state
  isRefreshing: boolean = false;
  showTemplates: boolean = false;
  activeTemplateCategory: string = 'sales';
  showKpiBuilder: boolean = false;
  isEditMode: boolean = false;

  // Math reference for template
  Math = Math;

  // KPI Cards Data
  kpiCards: KpiCard[] = [
    {
      id: 'revenue',
      label: 'Total Revenue',
      value: '$127,450',
      icon: 'attach_money',
      change: 12.5,
      comparison: 'last month',
      period: 'This Month',
      progress: 85
    },
    {
      id: 'customers',
      label: 'Active Customers',
      value: '2,847',
      icon: 'people',
      change: 8.3,
      comparison: 'last month',
      period: 'Current',
      progress: 92
    },
    {
      id: 'conversion',
      label: 'Conversion Rate',
      value: '24.8%',
      icon: 'trending_up',
      change: 3.2,
      comparison: 'last quarter',
      period: 'Q4 2024'
    },
    {
      id: 'tickets',
      label: 'Support Tickets Resolved',
      value: '1,234',
      icon: 'check_circle',
      change: 15.7,
      comparison: 'last week',
      period: 'This Week',
      progress: 78
    },
    {
      id: 'ai_performance',
      label: 'AI Agent Performance',
      value: '94.2%',
      icon: 'psychology',
      change: 2.1,
      comparison: 'last month',
      period: 'Current Score'
    },
    {
      id: 'leads',
      label: 'Lead Generation Rate',
      value: '456',
      icon: 'track_changes',
      change: -2.3,
      comparison: 'last month',
      period: 'This Month',
      progress: 67
    },
    {
      id: 'satisfaction',
      label: 'Customer Satisfaction',
      value: '4.8/5',
      icon: 'star',
      change: 5.4,
      comparison: 'last quarter',
      period: 'Q4 2024'
    },
    {
      id: 'response_time',
      label: 'Avg Response Time',
      value: '2.3min',
      icon: 'schedule',
      change: -12.8,
      comparison: 'last week',
      period: 'This Week'
    }
  ];

  // Template Categories
  templateCategories: TemplateCategory[] = [
    { id: 'sales', name: 'Sales & Revenue', icon: 'trending_up' },
    { id: 'customer', name: 'Customer Service', icon: 'support_agent' },
    { id: 'marketing', name: 'Marketing & Leads', icon: 'campaign' },
    { id: 'ai', name: 'AI Performance', icon: 'psychology' }
  ];

  // KPI Templates
  kpiTemplates: KpiTemplate[] = [
    {
      id: 'sales_overview',
      name: 'Sales Overview',
      description: 'Complete sales performance dashboard with revenue, conversion, and growth metrics',
      category: 'sales',
      preview: [
        { icon: 'attach_money', label: 'Revenue' },
        { icon: 'trending_up', label: 'Growth' },
        { icon: 'analytics', label: 'Conversion' },
        { icon: 'account_balance', label: 'Profit' }
      ]
    },
    {
      id: 'customer_support',
      name: 'Customer Support',
      description: 'Track support tickets, response times, and customer satisfaction',
      category: 'customer',
      preview: [
        { icon: 'support_agent', label: 'Tickets' },
        { icon: 'schedule', label: 'Response Time' },
        { icon: 'star', label: 'Satisfaction' },
        { icon: 'check_circle', label: 'Resolution Rate' }
      ]
    },
    {
      id: 'marketing_funnel',
      name: 'Marketing Funnel',
      description: 'Monitor lead generation, campaign performance, and conversion rates',
      category: 'marketing',
      preview: [
        { icon: 'campaign', label: 'Campaigns' },
        { icon: 'track_changes', label: 'Leads' },
        { icon: 'visibility', label: 'Impressions' },
        { icon: 'mouse', label: 'Clicks' }
      ]
    },
    {
      id: 'ai_analytics',
      name: 'AI Analytics',
      description: 'AI agent performance, training metrics, and automation insights',
      category: 'ai',
      preview: [
        { icon: 'psychology', label: 'AI Score' },
        { icon: 'smart_toy', label: 'Automation' },
        { icon: 'model_training', label: 'Training' },
        { icon: 'insights', label: 'Insights' }
      ]
    }
  ];

  dashboardTabs: DashboardTab[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      iconName: 'dashboard',
      route: '/dashboard',
      description: 'Main overview and analytics'
    },
    {
      id: 'conversations',
      label: 'Conversations',
      iconName: 'chat',
      route: '/dashboard/conversations',
      description: 'Chat management and history'
    },
    {
      id: 'meetings',
      label: 'Meetings',
      iconName: 'video_call',
      route: '/dashboard/meetings',
      description: 'Video conferencing and meeting management'
    },
    {
      id: 'support-tickets',
      label: 'Support Tickets',
      iconName: 'confirmation_number',
      route: '/dashboard/support-tickets',
      description: 'Customer service ticket management'
    },
    {
      id: 'business-intelligence',
      label: 'Business Intelligence',
      iconName: 'psychology',
      route: '/dashboard/business-intelligence',
      description: 'AI-powered analytics and insights'
    },
    {
      id: 'lead-management',
      label: 'Lead Management',
      iconName: 'track_changes',
      route: '/dashboard/lead-management',
      description: 'Sales prospect tracking and management'
    },
    {
      id: 'customer-profiles',
      label: 'Customer Profiles',
      iconName: 'people',
      route: '/dashboard/customer-profiles',
      description: 'Customer relationship management'
    },
    {
      id: 'ai-training',
      label: 'AI Training',
      iconName: 'smart_toy',
      route: '/dashboard/ai-training',
      description: 'Model configuration and training data'
    },
    {
      id: 'reports-analytics',
      label: 'Reports & Analytics',
      iconName: 'analytics',
      route: '/dashboard/reports-analytics',
      description: 'Detailed reporting dashboard'
    },
    {
      id: 'integrations',
      label: 'Integrations',
      iconName: 'extension',
      route: '/dashboard/integrations',
      description: 'Third-party service connections'
    },
    {
      id: 'settings',
      label: 'Settings',
      iconName: 'settings',
      route: '/dashboard/settings',
      description: 'User preferences and system configuration'
    }
  ];

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Set active tab based on current route
    const currentRoute = this.router.url;
    const activeTabFromRoute = this.dashboardTabs.find(tab => 
      currentRoute.includes(tab.route) && tab.route !== '/dashboard'
    );
    
    if (activeTabFromRoute) {
      this.activeTab = activeTabFromRoute.id;
    }
  }

  selectTab(tabId: string): void {
    this.activeTab = tabId;
    const selectedTab = this.dashboardTabs.find(tab => tab.id === tabId);
    if (selectedTab) {
      this.router.navigate([selectedTab.route]);
    }
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  isTabActive(tabId: string): boolean {
    return this.activeTab === tabId;
  }

  getActiveTab(): DashboardTab {
    return this.dashboardTabs.find(tab => tab.id === this.activeTab) || this.dashboardTabs[0];
  }

  getActiveTabLabel(): string {
    return this.getActiveTab().label;
  }

  getActiveTabDescription(): string {
    return this.getActiveTab().description;
  }

  getActiveTabIcon(): string {
    return this.getActiveTab().iconName;
  }

  // Dashboard Methods
  getCurrentTime(): string {
    return new Date().toLocaleTimeString();
  }

  refreshDashboard(): void {
    this.isRefreshing = true;
    // Simulate API call
    setTimeout(() => {
      this.isRefreshing = false;
      // Update KPI values with random variations
      this.kpiCards.forEach(kpi => {
        const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
        kpi.change = Math.round((kpi.change + variation) * 10) / 10;
      });
    }, 2000);
  }

  toggleTemplates(): void {
    this.showTemplates = !this.showTemplates;
  }

  selectTemplateCategory(categoryId: string): void {
    this.activeTemplateCategory = categoryId;
  }

  getFilteredTemplates(): KpiTemplate[] {
    return this.kpiTemplates.filter(template => template.category === this.activeTemplateCategory);
  }

  applyTemplate(template: KpiTemplate): void {
    console.log('Applying template:', template.name);
    // Implementation for applying template
  }

  openKpiBuilder(): void {
    this.showKpiBuilder = true;
  }

  closeKpiBuilder(): void {
    this.showKpiBuilder = false;
  }

  onCreateKpi(newKpi: KpiCard): void {
    this.kpiCards.push(newKpi);
    this.showKpiBuilder = false;
  }

  editLayout(): void {
    this.isEditMode = true;
  }

  onSaveLayout(updatedKpis: KpiCard[]): void {
    this.kpiCards = updatedKpis;
    this.isEditMode = false;
  }

  onCancelEdit(): void {
    this.isEditMode = false;
  }

  saveDashboard(): void {
    console.log('Saving dashboard configuration');
    // Implementation for saving dashboard
  }
}
