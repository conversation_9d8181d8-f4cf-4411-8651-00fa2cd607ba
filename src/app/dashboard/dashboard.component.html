<div class="dashboard-container">
  <!-- Dashboard Layout -->
  <div class="dashboard-layout">
    <!-- Sidebar Navigation -->
    <aside class="sidebar" [class.collapsed]="sidebarCollapsed">
      <!-- Sidebar Header -->
      <div class="sidebar-header">
        <div class="logo-section">
          <div class="logo-wrapper">
            <img src="/insphera-logo.png" alt="InspheraAI Logo" class="sidebar-logo" />
          </div>
          <h1 class="sidebar-title" *ngIf="!sidebarCollapsed">InspheraAI</h1>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <nav class="sidebar-nav" role="navigation">
        <ul class="nav-list">
          <li 
            *ngFor="let tab of dashboardTabs" 
            class="nav-item"
            [class.active]="isTabActive(tab.id)"
          >
            <button
              class="nav-link"
              (click)="selectTab(tab.id)"
              [attr.aria-current]="isTabActive(tab.id) ? 'page' : null"
              [title]="sidebarCollapsed ? tab.label + ': ' + tab.description : tab.description"
            >
              <mat-icon class="nav-icon">{{ tab.iconName }}</mat-icon>
              <span class="nav-text" *ngIf="!sidebarCollapsed">{{ tab.label }}</span>
              <div class="nav-indicator" *ngIf="isTabActive(tab.id)"></div>
            </button>
          </li>
        </ul>
      </nav>

      <!-- Sidebar Toggle Container -->
      <div class="sidebar-toggle-container">
        <button
          class="sidebar-toggle"
          (click)="toggleSidebar()"
          [attr.aria-label]="sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
        >
          <span class="toggle-icon">{{ sidebarCollapsed ? '→' : '←' }}</span>
        </button>
      </div>

      <!-- Sidebar Footer -->
      <div class="sidebar-footer" *ngIf="!sidebarCollapsed">
        <div class="user-info">
          <div class="user-avatar">👤</div>
          <div class="user-details">
            <span class="user-name">AI Agent</span>
            <span class="user-role">Dashboard User</span>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
      <div class="content-header">
        <h1 class="page-title">
          {{ getActiveTabLabel() }}
        </h1>
        <p class="page-description">
          {{ getActiveTabDescription() }}
        </p>
      </div>

      <!-- Content Area -->
      <div class="content-body">
        <!-- Dashboard Content -->
        <div *ngIf="activeTab === 'dashboard'" class="tab-content">
          <!-- Dashboard Header -->
          <app-dashboard-header
            [isRefreshing]="isRefreshing"
            (refresh)="refreshDashboard()">
          </app-dashboard-header>

          <!-- KPI Grid -->
          <app-kpi-grid [kpiCards]="kpiCards"></app-kpi-grid>

          <!-- Action Bar -->
          <app-action-bar
            (addKpi)="openKpiBuilder()"
            (toggleTemplates)="toggleTemplates()"
            (editLayout)="editLayout()"
            (saveDashboard)="saveDashboard()">
          </app-action-bar>

          <!-- Templates Section -->
          <app-templates-section
            [showTemplates]="showTemplates"
            [activeTemplateCategory]="activeTemplateCategory"
            [templateCategories]="templateCategories"
            [kpiTemplates]="kpiTemplates"
            (toggleTemplates)="toggleTemplates()"
            (selectCategory)="selectTemplateCategory($event)"
            (applyTemplate)="applyTemplate($event)">
          </app-templates-section>

          <!-- KPI Builder Modal -->
          <app-kpi-builder
            [isOpen]="showKpiBuilder"
            (close)="closeKpiBuilder()"
            (createKpi)="onCreateKpi($event)">
          </app-kpi-builder>

          <!-- Edit Layout Mode -->
          <app-edit-layout
            [isEditMode]="isEditMode"
            [kpiCards]="kpiCards"
            (saveLayout)="onSaveLayout($event)"
            (cancelEdit)="onCancelEdit()">
          </app-edit-layout>
        </div>

        <!-- Conversations Dashboard -->
        <div *ngIf="activeTab === 'conversations'" class="tab-content conversations-tab">
          <app-conversations-dashboard></app-conversations-dashboard>
        </div>

        <!-- Placeholder for other tabs -->
        <div *ngIf="activeTab !== 'dashboard' && activeTab !== 'conversations'" class="tab-content">
          <div class="placeholder-content">
            <div class="placeholder-icon">
              <mat-icon class="placeholder-icon-large">{{ getActiveTabIcon() }}</mat-icon>
            </div>
            <h3>{{ getActiveTabLabel() }}</h3>
            <p>{{ getActiveTabDescription() }}</p>
            <div class="coming-soon">
              <span>Content implementation coming soon...</span>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>
