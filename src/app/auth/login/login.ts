import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ToastService } from '../../services/toast.service';
import { SharedHeaderComponent } from '../../components/shared-header/shared-header.component';

@Component({
  selector: 'app-login',
  imports: [ReactiveFormsModule, CommonModule, RouterModule, SharedHeaderComponent],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})
export class Login implements OnInit {
  loginForm!: FormGroup;
  isLoading = false;
  isSubmitted = false;
  hasError = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  navigateBack(): void {
    this.router.navigate(['/home']);
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isFieldValid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.valid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return `Password must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      email: 'Email Address',
      password: 'Password'
    };
    return displayNames[fieldName] || fieldName;
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.hasError = false;

      // Simulate API call
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90% success rate

        this.isLoading = false;
        if (success) {
          this.isSubmitted = true;
          console.log('Login successful:', this.loginForm.value);

          // Show registration form as toast overlay
          setTimeout(() => {
            this.toastService.showRegisterForm();
          }, 1000);
        } else {
          this.hasError = true;
        }
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }

  // Social authentication methods
  signInWithGoogle(): void {
    console.log('Google Sign-In clicked');
    // Implement Google OAuth integration
    this.simulateSocialAuth('Google');
  }

  signInWithGitHub(): void {
    console.log('GitHub Sign-In clicked');
    // Implement GitHub OAuth integration
    this.simulateSocialAuth('GitHub');
  }

  signInWithFacebook(): void {
    console.log('Facebook Sign-In clicked');
    // Implement Facebook OAuth integration
    this.simulateSocialAuth('Facebook');
  }

  signInWithApple(): void {
    console.log('Apple Sign-In clicked');
    // Implement Apple OAuth integration
    this.simulateSocialAuth('Apple');
  }

  private simulateSocialAuth(provider: string): void {
    console.log(`Authenticating with ${provider}...`);
    // Simulate social authentication
    setTimeout(() => {
      console.log(`${provider} authentication successful`);
      this.router.navigate(['/home']);
    }, 1500);
  }

  resetForm(): void {
    this.hasError = false;
    this.isSubmitted = false;
    this.isLoading = false;
    this.initializeForm();
  }
}
