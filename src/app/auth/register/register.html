<div class="auth-container">
  <!-- Background geometric elements -->
  <div class="geometric-bg">
    <div class="circle circle-1"></div>
    <div class="circle circle-2"></div>
    <div class="circle circle-3"></div>
    <div class="grid-pattern"></div>
  </div>

  <!-- Shared Header -->
  <app-shared-header
    pageType="auth"
    appTitle="InspheraAI"
    [showNavigation]="true">
  </app-shared-header>

  <!-- Main Auth Section -->
  <main class="auth-section">
    <div class="auth-container-inner">
      <div class="auth-header">
        <div class="auth-badge">
          <span class="badge-text">Step {{ currentStep }} of {{ totalSteps }}</span>
        </div>
        <h1 class="auth-title">
          <span class="title-text">Create Account</span>
        </h1>
        <p class="auth-subtitle">
          Join <PERSON><PERSON>hera<PERSON><PERSON> and unlock the future of AI-powered sales automation.
        </p>
      </div>

      <!-- Step Progress Indicator -->
      <div class="step-progress">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
        </div>
        <div class="step-indicators">
          <div
            *ngFor="let step of [1,2,3,4]; let i = index"
            class="step-indicator"
            [class.active]="step === currentStep"
            [class.completed]="isStepCompleted(step)"
            [class.accessible]="canAccessStep(step)"
            [class.blocked]="!canAccessStep(step)"
            (click)="onStepClick(step)"
            (touchstart)="onStepClick(step)"
          >
            <span class="step-number">
              <span *ngIf="isStepCompleted(step)" class="step-check">✓</span>
              <span *ngIf="!isStepCompleted(step)">{{ step }}</span>
            </span>
            <span class="step-label">
              {{ step === 1 ? 'Basic Info' : step === 2 ? 'Business' : step === 3 ? 'AI Preferences' : 'Verification' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Multi-Step Registration Form -->
      <div class="auth-form">
        <!-- Step 1: Basic Information -->
        <div class="step-content" *ngIf="currentStep === 1">
          <div class="step-header">
            <h2 class="step-title">Basic Information</h2>
            <p class="step-description">Let's start with your basic details</p>
          </div>

          <form [formGroup]="step1Form" class="step-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="fullName">
                  Full Name <span class="required">*</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="fullName"
                    class="form-input"
                    formControlName="fullName"
                    placeholder="Enter your full name"
                    [class.error]="isFieldInvalid('fullName')"
                    [class.success]="isFieldValid('fullName')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('fullName')">
                  {{ getFieldError('fullName') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="email">
                  Email Address <span class="required">*</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="email"
                    id="email"
                    class="form-input"
                    formControlName="email"
                    placeholder="Enter your email address"
                    [class.error]="isFieldInvalid('email')"
                    [class.success]="isFieldValid('email')"
                    autocomplete="email"
                    inputmode="email"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('email')">
                  {{ getFieldError('email') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="password">
                  Password <span class="required">*</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="password"
                    id="password"
                    class="form-input"
                    formControlName="password"
                    placeholder="Create a strong password"
                    [class.error]="isFieldInvalid('password')"
                    [class.success]="isFieldValid('password')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('password')">
                  {{ getFieldError('password') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="confirmPassword">
                  Confirm Password <span class="required">*</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="password"
                    id="confirmPassword"
                    class="form-input"
                    formControlName="confirmPassword"
                    placeholder="Confirm your password"
                    [class.error]="isFieldInvalid('confirmPassword')"
                    [class.success]="isFieldValid('confirmPassword')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('confirmPassword')">
                  {{ getFieldError('confirmPassword') }}
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Step 2: Business Profile -->
        <div class="step-content" *ngIf="currentStep === 2">
          <div class="step-header">
            <h2 class="step-title">Business Profile</h2>
            <p class="step-description">Tell us about your business</p>
          </div>

          <form [formGroup]="step2Form" class="step-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="companyName">
                  Company Name <span class="required">*</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="companyName"
                    class="form-input"
                    formControlName="companyName"
                    placeholder="Enter your company name"
                    [class.error]="isFieldInvalid('companyName')"
                    [class.success]="isFieldValid('companyName')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('companyName')">
                  {{ getFieldError('companyName') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="businessType">
                  Business Type <span class="required">*</span>
                </label>
                <div class="select-wrapper">
                  <select
                    id="businessType"
                    class="form-select"
                    formControlName="businessType"
                    [class.error]="isFieldInvalid('businessType')"
                    [class.success]="isFieldValid('businessType')"
                  >
                    <option value="">Select your business type</option>
                    <option value="e-commerce">E-commerce & Retail</option>
                    <option value="saas">SaaS & Technology</option>
                    <option value="healthcare">Healthcare & Medical</option>
                    <option value="finance">Finance & Banking</option>
                    <option value="real-estate">Real Estate</option>
                    <option value="education">Education & Training</option>
                    <option value="consulting">Consulting & Professional Services</option>
                    <option value="manufacturing">Manufacturing & Industrial</option>
                    <option value="hospitality">Hospitality & Travel</option>
                    <option value="nonprofit">Non-profit & Government</option>
                    <option value="other">Other</option>
                  </select>
                  <div class="select-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('businessType')">
                  {{ getFieldError('businessType') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="companySize">
                  Company Size <span class="required">*</span>
                </label>
                <div class="select-wrapper">
                  <select
                    id="companySize"
                    class="form-select"
                    formControlName="companySize"
                    [class.error]="isFieldInvalid('companySize')"
                    [class.success]="isFieldValid('companySize')"
                  >
                    <option value="">Select company size</option>
                    <option value="1-10">1-10 employees</option>
                    <option value="11-50">11-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-1000">201-1000 employees</option>
                    <option value="1000+">1000+ employees</option>
                  </select>
                  <div class="select-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('companySize')">
                  {{ getFieldError('companySize') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="industryRole">
                  Your Role <span class="required">*</span>
                </label>
                <div class="select-wrapper">
                  <select
                    id="industryRole"
                    class="form-select"
                    formControlName="industryRole"
                    [class.error]="isFieldInvalid('industryRole')"
                    [class.success]="isFieldValid('industryRole')"
                  >
                    <option value="">Select your role</option>
                    <option value="ceo">CEO/Founder</option>
                    <option value="cto">CTO/Technical Lead</option>
                    <option value="sales-manager">Sales Manager</option>
                    <option value="customer-service">Customer Service Manager</option>
                    <option value="marketing">Marketing Manager</option>
                    <option value="operations">Operations Manager</option>
                    <option value="developer">Developer/Engineer</option>
                    <option value="analyst">Business Analyst</option>
                    <option value="other">Other</option>
                  </select>
                  <div class="select-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('industryRole')">
                  {{ getFieldError('industryRole') }}
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Step 3: AI Preferences & Use Cases -->
        <div class="step-content" *ngIf="currentStep === 3">
          <div class="step-header">
            <h2 class="step-title">AI Preferences</h2>
            <p class="step-description">How do you plan to use InspheraAI?</p>
          </div>

          <form [formGroup]="step3Form" class="step-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="primaryUseCase">
                  Primary Use Case <span class="required">*</span>
                </label>
                <div class="select-wrapper">
                  <select
                    id="primaryUseCase"
                    class="form-select"
                    formControlName="primaryUseCase"
                    [class.error]="isFieldInvalid('primaryUseCase')"
                    [class.success]="isFieldValid('primaryUseCase')"
                  >
                    <option value="">Select primary use case</option>
                    <option value="customer-service">Customer Service Automation</option>
                    <option value="sales-automation">Sales Process Automation</option>
                    <option value="business-intelligence">Business Intelligence & Analytics</option>
                    <option value="all">All of the Above</option>
                  </select>
                  <div class="select-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('primaryUseCase')">
                  {{ getFieldError('primaryUseCase') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="currentTools">
                  Current Tools/Platforms <span class="optional">(Optional)</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="currentTools"
                    class="form-input"
                    formControlName="currentTools"
                    placeholder="e.g., Salesforce, HubSpot, Zendesk"
                    [class.error]="isFieldInvalid('currentTools')"
                    [class.success]="isFieldValid('currentTools')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('currentTools')">
                  {{ getFieldError('currentTools') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="integrationPreferences">
                  Integration Preferences <span class="optional">(Optional)</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="integrationPreferences"
                    class="form-input"
                    formControlName="integrationPreferences"
                    placeholder="e.g., API, Webhook, Direct Integration"
                    [class.error]="isFieldInvalid('integrationPreferences')"
                    [class.success]="isFieldValid('integrationPreferences')"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('integrationPreferences')">
                  {{ getFieldError('integrationPreferences') }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="expectedVolume">
                  Expected Volume/Scale <span class="required">*</span>
                </label>
                <div class="select-wrapper">
                  <select
                    id="expectedVolume"
                    class="form-select"
                    formControlName="expectedVolume"
                    [class.error]="isFieldInvalid('expectedVolume')"
                    [class.success]="isFieldValid('expectedVolume')"
                  >
                    <option value="">Select expected volume</option>
                    <option value="low">Low (< 1,000 interactions/month)</option>
                    <option value="medium">Medium (1,000 - 10,000 interactions/month)</option>
                    <option value="high">High (10,000 - 100,000 interactions/month)</option>
                    <option value="enterprise">Enterprise (100,000+ interactions/month)</option>
                  </select>
                  <div class="select-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('expectedVolume')">
                  {{ getFieldError('expectedVolume') }}
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Step 4: Verification & Preferences -->
        <div class="step-content" *ngIf="currentStep === 4">
          <div class="step-header">
            <h2 class="step-title">Verification & Preferences</h2>
            <p class="step-description">Complete your account setup</p>
          </div>

          <form [formGroup]="step4Form" class="step-form">
            <!-- OTP Verification Section -->
            <div class="otp-section" *ngIf="otpSent">
              <div class="otp-header">
                <h3 class="otp-title">Email Verification</h3>
                <p class="otp-description">
                  We've sent a 6-digit verification code to {{ step1Form.get('email')?.value }}
                </p>
              </div>

              <div class="form-group">
                <label class="form-label" for="otpCode">
                  Verification Code <span class="required">*</span>
                </label>
                <div class="otp-input-container">
                  <input
                    *ngFor="let digit of otpDigits; let i = index"
                    type="text"
                    class="otp-digit-input"
                    [id]="'otp-' + i"
                    [(ngModel)]="otpDigits[i]"
                    (input)="onOTPDigitInput($event, i)"
                    (keydown)="onOTPKeyDown($event, i)"
                    (paste)="onOTPPaste($event, i)"
                    maxlength="1"
                    [class.success]="otpVerified"
                    [class.filled]="otpDigits[i]"
                    inputmode="numeric"
                    pattern="[0-9]*"
                    autocomplete="one-time-code"
                  />
                </div>
                <div class="otp-status" *ngIf="otpVerified" class="success-message">
                  ✓ Email verified successfully
                </div>
                <div class="otp-actions" *ngIf="!otpVerified">
                  <button type="button" class="resend-otp" (click)="resendOTP()">
                    Resend Code
                  </button>
                </div>
              </div>
            </div>

            <!-- Additional Preferences -->
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="phoneNumber">
                  Phone Number <span class="optional">(Optional)</span>
                </label>
                <div class="input-wrapper">
                  <input
                    type="tel"
                    id="phoneNumber"
                    class="form-input"
                    formControlName="phoneNumber"
                    placeholder="Enter your phone number"
                    [class.error]="isFieldInvalid('phoneNumber')"
                    [class.success]="isFieldValid('phoneNumber')"
                    autocomplete="tel"
                    inputmode="tel"
                  />
                  <div class="input-glow"></div>
                </div>
                <div class="error-message" *ngIf="isFieldInvalid('phoneNumber')">
                  {{ getFieldError('phoneNumber') }}
                </div>
              </div>

              <!-- Terms and Privacy Checkboxes -->
              <div class="checkbox-group">
                <div class="checkbox-item">
                  <label for="termsAccepted" class="checkbox-label">
                    <input
                      type="checkbox"
                      id="termsAccepted"
                      formControlName="termsAccepted"
                      class="checkbox-input"
                    />
                    <span class="checkbox-custom">
                      <span class="checkbox-check">✓</span>
                    </span>
                    <span class="checkbox-text">
                      I agree to the <a href="#" class="link" (click)="$event.preventDefault()">Terms of Service</a> <span class="required">*</span>
                    </span>
                  </label>
                  <div class="error-message" *ngIf="isFieldInvalid('termsAccepted')">
                    {{ getFieldError('termsAccepted') }}
                  </div>
                </div>

                <div class="checkbox-item">
                  <label for="privacyAccepted" class="checkbox-label">
                    <input
                      type="checkbox"
                      id="privacyAccepted"
                      formControlName="privacyAccepted"
                      class="checkbox-input"
                    />
                    <span class="checkbox-custom">
                      <span class="checkbox-check">✓</span>
                    </span>
                    <span class="checkbox-text">
                      I agree to the <a href="#" class="link" (click)="$event.preventDefault()">Privacy Policy</a> <span class="required">*</span>
                    </span>
                  </label>
                  <div class="error-message" *ngIf="isFieldInvalid('privacyAccepted')">
                    {{ getFieldError('privacyAccepted') }}
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Step Navigation -->
        <div class="step-navigation">
          <button
            type="button"
            class="nav-button secondary"
            *ngIf="currentStep > 1"
            (click)="previousStep()"
            (touchstart)="previousStep()"
          >
            <span class="nav-icon">←</span>
            <span class="nav-text">Previous</span>
          </button>

          <button
            type="button"
            class="nav-button primary"
            [disabled]="!isStepValid || isLoading"
            [class.loading]="isLoading"
            (click)="onSubmit()"
            (touchstart)="onSubmit()"
          >
            <span class="nav-text" *ngIf="!isLoading">
              {{ currentStep === totalSteps ? 'Create Account' : 'Next' }}
            </span>
            <span class="nav-text" *ngIf="isLoading">
              {{ currentStep === totalSteps ? 'Creating Account...' : 'Processing...' }}
            </span>
            <span class="nav-icon" *ngIf="currentStep < totalSteps && !isLoading">→</span>
            <div class="loading-spinner" *ngIf="isLoading"></div>
          </button>
        </div>

        <!-- Social Authentication (only on step 1) -->
        <div class="social-auth" *ngIf="currentStep === 1">
          <div class="auth-divider">
            <span class="divider-text">or</span>
          </div>

          <h3 class="social-title">Sign up with</h3>

          <div class="social-buttons">
            <button class="social-btn google" (click)="signUpWithGoogle()" (touchstart)="signUpWithGoogle()">
              <div class="social-icon">
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              </div>
              <span class="social-text">Continue with Google</span>
            </button>

            <button class="social-btn github" (click)="signUpWithGitHub()" (touchstart)="signUpWithGitHub()">
              <div class="social-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </div>
              <span class="social-text">Continue with GitHub</span>
            </button>

            <button class="social-btn facebook" (click)="signUpWithFacebook()" (touchstart)="signUpWithFacebook()">
              <div class="social-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </div>
              <span class="social-text">Continue with Facebook</span>
            </button>

            <button class="social-btn apple" (click)="signUpWithApple()" (touchstart)="signUpWithApple()">
              <div class="social-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"/>
                </svg>
              </div>
              <span class="social-text">Continue with Apple</span>
            </button>
          </div>
        </div>

        <div class="auth-footer" *ngIf="currentStep === 1">
          <p class="auth-link">
            Already have an account?
            <a routerLink="/login" class="link">Sign in here</a>
          </p>
        </div>
      </div>
    </div>
  </main>

  <!-- Corner decorative elements -->
  <div class="corner-elements">
    <div class="corner-element top-left"></div>
    <div class="corner-element top-right"></div>
    <div class="corner-element bottom-left"></div>
    <div class="corner-element bottom-right"></div>
  </div>
</div>

