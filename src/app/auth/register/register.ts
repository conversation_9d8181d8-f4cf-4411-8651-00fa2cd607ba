import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SharedHeaderComponent } from '../../components/shared-header/shared-header.component';

@Component({
  selector: 'app-register',
  imports: [ReactiveFormsModule, CommonModule, RouterModule, FormsModule, SharedHeaderComponent],
  templateUrl: './register.html',
  styleUrl: './register.scss'
})
export class RegisterComponent implements OnInit {
  // Toast mode support
  @Input() isToastMode = false;
  @Output() closeToast = new EventEmitter<void>();

  // Multi-step form management
  currentStep = 1;
  totalSteps = 4;

  // Form groups for each step
  step1Form!: FormGroup; // Basic Information
  step2Form!: FormGroup; // Business Profile
  step3Form!: FormGroup; // AI Preferences
  step4Form!: FormGroup; // Verification & Preferences

  // State management
  isLoading = false;
  isSubmitted = false;
  hasError = false;
  isStepValid = false;

  // OTP verification
  otpCode = '';
  otpDigits: string[] = ['', '', '', '', '', ''];
  otpSent = false;
  otpVerified = false;
registerForm: any;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.checkStepValidity();
  }

  private initializeForms(): void {
    // Step 1: Basic Information
    this.step1Form = this.formBuilder.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    // Step 2: Business Profile
    this.step2Form = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      businessType: ['', [Validators.required]],
      companySize: ['', [Validators.required]],
      industryRole: ['', [Validators.required]]
    });

    // Step 3: AI Preferences & Use Cases
    this.step3Form = this.formBuilder.group({
      primaryUseCase: ['', [Validators.required]],
      currentTools: [''],
      integrationPreferences: [''],
      expectedVolume: ['', [Validators.required]]
    });

    // Step 4: Verification & Preferences
    this.step4Form = this.formBuilder.group({
      phoneNumber: [''],
      communicationPreferences: [[]],
      termsAccepted: [false, [Validators.requiredTrue]],
      privacyAccepted: [false, [Validators.requiredTrue]]
    });

    // Subscribe to form changes to update step validity
    this.step1Form.valueChanges.subscribe(() => this.checkStepValidity());
    this.step2Form.valueChanges.subscribe(() => this.checkStepValidity());
    this.step3Form.valueChanges.subscribe(() => this.checkStepValidity());
    this.step4Form.valueChanges.subscribe(() => this.checkStepValidity());
  }

  // Multi-step navigation methods
  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 1: return this.step1Form;
      case 2: return this.step2Form;
      case 3: return this.step3Form;
      case 4: return this.step4Form;
      default: return this.step1Form;
    }
  }

  checkStepValidity(): void {
    const currentForm = this.getCurrentForm();
    this.isStepValid = currentForm.valid;

    // Special handling for step 4 (OTP verification)
    if (this.currentStep === 4) {
      this.isStepValid = currentForm.valid && this.otpVerified;
    }
  }

  nextStep(): void {
    const currentForm = this.getCurrentForm();

    // Mark all fields as touched to show validation errors
    Object.keys(currentForm.controls).forEach(key => {
      currentForm.get(key)?.markAsTouched();
    });

    if (currentForm.valid) {
      // Special handling for step 4 - require OTP verification
      if (this.currentStep === 4 && !this.otpVerified) {
        return; // Don't proceed if OTP not verified
      }

      if (this.currentStep === 1) {
        // Send OTP after step 1 completion
        this.sendOTP();
      }

      if (this.currentStep < this.totalSteps) {
        this.currentStep++;
        this.checkStepValidity();
      }
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
      this.checkStepValidity();
    }
  }

  goToStep(step: number): void {
    // Only allow going to a step if all previous steps are completed
    if (step >= 1 && step <= this.totalSteps && this.canAccessStep(step)) {
      this.currentStep = step;
      this.checkStepValidity();
    }
  }

  onStepClick(step: number): void {
    // Simplified click handler for better mobile compatibility
    if (this.canAccessStep(step)) {
      this.goToStep(step);
    }
  }

  canAccessStep(step: number): boolean {
    // Always allow access to step 1
    if (step === 1) return true;

    // For other steps, check if all previous steps are completed
    for (let i = 1; i < step; i++) {
      if (!this.isStepCompleted(i)) {
        return false;
      }
    }
    return true;
  }

  isStepCompleted(step: number): boolean {
    switch (step) {
      case 1:
        return this.step1Form.valid;
      case 2:
        return this.step2Form.valid;
      case 3:
        return this.step3Form.valid;
      case 4:
        return this.step4Form.valid && this.otpVerified;
      default:
        return false;
    }
  }

  // Custom validator for password confirmation
  private passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  // OTP functionality
  sendOTP(): void {
    const email = this.step1Form.get('email')?.value;
    if (email) {
      console.log('Sending OTP to:', email);
      // Simulate OTP sending
      setTimeout(() => {
        this.otpSent = true;
        console.log('OTP sent successfully');
      }, 1000);
    }
  }

  verifyOTP(): void {
    this.otpCode = this.otpDigits.join('');
    if (this.otpCode.length === 6) {
      this.isLoading = true;
      // Simulate OTP verification
      setTimeout(() => {
        this.isLoading = false;
        // For demo purposes, accept any 6-digit code
        this.otpVerified = true;
        this.checkStepValidity();
        console.log('OTP verified successfully');
      }, 1500);
    }
  }

  onOTPDigitInput(event: any, index: number): void {
    const value = event.target.value.replace(/\D/g, ''); // Only digits

    if (value.length > 1) {
      // Handle paste or multiple characters
      this.handleOTPPaste(value, index);
      return;
    }

    this.otpDigits[index] = value;

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`) as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
      }
    }

    // Check if OTP is complete
    this.otpCode = this.otpDigits.join('');
    if (this.otpCode.length === 6) {
      this.verifyOTP();
    }
  }

  onOTPKeyDown(event: KeyboardEvent, index: number): void {
    // Handle backspace
    if (event.key === 'Backspace' && !this.otpDigits[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`) as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
        this.otpDigits[index - 1] = '';
      }
    }

    // Handle arrow keys
    if (event.key === 'ArrowLeft' && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`) as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
      }
    }

    if (event.key === 'ArrowRight' && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`) as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
      }
    }
  }

  onOTPPaste(event: ClipboardEvent, index: number): void {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    this.handleOTPPaste(pastedData, index);
  }

  private handleOTPPaste(data: string, startIndex: number): void {
    const digits = data.replace(/\D/g, '').slice(0, 6); // Only digits, max 6

    for (let i = 0; i < digits.length && (startIndex + i) < 6; i++) {
      this.otpDigits[startIndex + i] = digits[i];
    }

    // Focus the next empty input or the last input
    const nextEmptyIndex = this.otpDigits.findIndex(digit => !digit);
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5;

    const targetInput = document.getElementById(`otp-${focusIndex}`) as HTMLInputElement;
    if (targetInput) {
      targetInput.focus();
    }

    // Check if OTP is complete
    this.otpCode = this.otpDigits.join('');
    if (this.otpCode.length === 6) {
      this.verifyOTP();
    }
  }

  resendOTP(): void {
    this.otpDigits = ['', '', '', '', '', ''];
    this.otpCode = '';
    this.otpVerified = false;
    this.sendOTP();

    // Focus first input
    setTimeout(() => {
      const firstInput = document.getElementById('otp-0') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  navigateBack(): void {
    this.router.navigate(['/home']);
  }

  isFieldInvalid(fieldName: string): boolean {
    const currentForm = this.getCurrentForm();
    const field = currentForm.get(fieldName);
    const formErrors = currentForm.errors;

    // Check for password mismatch error specifically for confirmPassword field
    if (fieldName === 'confirmPassword' && formErrors?.['passwordMismatch']) {
      return !!(field && (field.dirty || field.touched));
    }

    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isFieldValid(fieldName: string): boolean {
    const currentForm = this.getCurrentForm();
    const field = currentForm.get(fieldName);
    const formErrors = currentForm.errors;

    // For confirmPassword, check both field validity and password match
    if (fieldName === 'confirmPassword') {
      return !!(field && field.valid && !formErrors?.['passwordMismatch'] && (field.dirty || field.touched));
    }

    return !!(field && field.valid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const currentForm = this.getCurrentForm();
    const field = currentForm.get(fieldName);
    const formErrors = currentForm.errors;

    if (field && (field.dirty || field.touched)) {
      // Check for password mismatch error
      if (fieldName === 'confirmPassword' && formErrors?.['passwordMismatch']) {
        return 'Passwords do not match';
      }

      if (field.errors) {
        if (field.errors['required']) {
          return `${this.getFieldDisplayName(fieldName)} is required`;
        }
        if (field.errors['email']) {
          return 'Please enter a valid email address';
        }
        if (field.errors['minlength']) {
          const requiredLength = field.errors['minlength'].requiredLength;
          if (fieldName === 'password') {
            return `Password must be at least ${requiredLength} characters`;
          }
          return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters`;
        }
        if (field.errors['requiredTrue']) {
          return `${this.getFieldDisplayName(fieldName)} must be accepted`;
        }
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      // Step 1 fields
      fullName: 'Full Name',
      email: 'Email Address',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      // Step 2 fields
      companyName: 'Company Name',
      businessType: 'Business Type',
      companySize: 'Company Size',
      industryRole: 'Industry Role',
      // Step 3 fields
      primaryUseCase: 'Primary Use Case',
      currentTools: 'Current Tools',
      integrationPreferences: 'Integration Preferences',
      expectedVolume: 'Expected Volume',
      // Step 4 fields
      phoneNumber: 'Phone Number',
      communicationPreferences: 'Communication Preferences',
      termsAccepted: 'Terms of Service',
      privacyAccepted: 'Privacy Policy'
    };
    return displayNames[fieldName] || fieldName;
  }

  onSubmit(): void {
    if (this.currentStep === this.totalSteps && this.step4Form.valid && this.otpVerified) {
      this.isLoading = true;
      this.hasError = false;

      // Collect all form data
      const registrationData = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value
      };

      // Simulate API call
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90% success rate

        this.isLoading = false;
        if (success) {
          this.isSubmitted = true;
          console.log('Registration successful:', registrationData);
          // Navigate to dashboard or home
          setTimeout(() => {
            this.router.navigate(['/home']);
          }, 1500);
        } else {
          this.hasError = true;
        }
      }, 2000);
    } else {
      // If not on final step, go to next step
      this.nextStep();
    }
  }

  // Social authentication methods
  signUpWithGoogle(): void {
    console.log('Google Sign-Up clicked');
    this.simulateSocialAuth('Google');
  }

  signUpWithGitHub(): void {
    console.log('GitHub Sign-Up clicked');
    this.simulateSocialAuth('GitHub');
  }

  signUpWithFacebook(): void {
    console.log('Facebook Sign-Up clicked');
    this.simulateSocialAuth('Facebook');
  }

  signUpWithApple(): void {
    console.log('Apple Sign-Up clicked');
    this.simulateSocialAuth('Apple');
  }

  private simulateSocialAuth(provider: string): void {
    console.log(`Creating account with ${provider}...`);
    // Simulate social authentication
    setTimeout(() => {
      console.log(`${provider} registration successful`);
      this.router.navigate(['/home']);
    }, 1500);
  }

  resetForm(): void {
    this.hasError = false;
    this.isSubmitted = false;
    this.isLoading = false;
    this.currentStep = 1;
    this.otpCode = '';
    this.otpDigits = ['', '', '', '', '', ''];
    this.otpSent = false;
    this.otpVerified = false;
    this.initializeForms();
  }
}
