// Import the same base styling as login page
@import '../login/login.scss';

// Multi-step registration specific styles
// Override auth-section to match home page spacing
.auth-section {
  padding: 2rem 3rem 2rem 3rem; // Match home page hero-section spacing exactly
}

.auth-container-inner {
  max-width: 600px; // Wider for multi-step form
}

// Step Progress Indicator
.step-progress {
  margin-bottom: 3rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin-bottom: 2rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  border-radius: 2px;
  transition: width 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.step-indicators {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;

  &.active {
    .step-number {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
      color: #000000;
      box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    }

    .step-label {
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
  }

  &.completed {
    .step-number {
      background: linear-gradient(135deg, rgba(74, 222, 128, 0.8) 0%, rgba(74, 222, 128, 0.6) 100%);
      color: #000000;
      border-color: rgba(74, 222, 128, 0.6);
    }

    .step-label {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  &.blocked {
    cursor: not-allowed;
    opacity: 0.4;

    .step-number {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
    }

    .step-label {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  &.accessible:hover:not(.active):not(.blocked) {
    .step-number {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
    }
  }
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  position: relative;
}

.step-check {
  font-size: 1rem;
  font-weight: bold;
  color: #000000;
}

.step-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  font-weight: 400;
  transition: all 0.3s ease;
}

// Step Content
.step-content {
  animation: stepSlideIn 0.5s ease-out forwards;
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-title {
  font-size: 1.8rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.95);
  margin: 0 0 0.8rem 0;
  letter-spacing: 0.02em;
}

.step-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 300;
}

.step-form {
  margin-bottom: 2rem;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
}

// OTP Section
.otp-section {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.otp-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.otp-title {
  font-size: 1.3rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.8rem 0;
}

.otp-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
}

// OTP Digit Inputs
.otp-input-container {
  display: flex;
  justify-content: center;
  gap: 0.8rem;
  margin: 1rem 0;
}

.otp-digit-input {
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);

  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.08);
    box-shadow:
      0 0 0 3px rgba(255, 255, 255, 0.1),
      0 4px 15px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }

  &.filled {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
  }

  &.success {
    border-color: #4ade80;
    background: rgba(74, 222, 128, 0.1);
    box-shadow: 0 0 15px rgba(74, 222, 128, 0.3);
  }
}

.otp-status {
  margin-top: 1rem;
  text-align: center;
}

.success-message {
  color: #4ade80;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.otp-actions {
  text-align: center;
  margin-top: 1rem;
}

.resend-otp {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }
}

.otp-status {
  margin-top: 1rem;
  text-align: center;
}

.success-message {
  color: #4ade80;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.otp-actions {
  text-align: center;
  margin-top: 1rem;
}

.resend-otp {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }
}

// Checkbox styling
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.checkbox-item {
  position: relative;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.5;
  position: relative;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + .checkbox-custom {
    background: linear-gradient(135deg, rgba(74, 222, 128, 0.9) 0%, rgba(74, 222, 128, 0.7) 100%);
    border-color: rgba(74, 222, 128, 0.8);

    .checkbox-check {
      opacity: 1;
      transform: scale(1);
    }
  }

  &:focus + .checkbox-custom {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.08);
  }
}

.checkbox-check {
  font-size: 12px;
  font-weight: bold;
  color: #000000;
  opacity: 0;
  transform: scale(0);
  transition: all 0.2s ease;
}

.checkbox-text {
  color: rgba(255, 255, 255, 0.8);

  .link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;

    &:hover {
      color: #ffffff;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
  }
}

// Step Navigation - Center the Next button properly
.step-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  min-height: 60px; // Ensure consistent height

  // Previous button positioning (when present)
  .nav-button.secondary {
    position: absolute;
    left: 0;
  }

  // Primary button always centered
  .nav-button.primary {
    position: relative;
    z-index: 2;
  }
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;

  &.secondary {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.4);
      color: #ffffff;
      transform: translateX(-2px);
    }
  }

  &.primary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.12) 100%);
      border-color: rgba(255, 255, 255, 0.4);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 255, 255, 0.15);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    &.loading {
      pointer-events: none;
    }
  }
}

.nav-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.nav-text {
  position: relative;
  z-index: 2;
}

// Select wrapper styling (for dropdowns)
.select-wrapper {
  position: relative;
  width: 100%;
}

.form-select {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  color: #ffffff;
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
  box-sizing: border-box;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;

  option {
    background: #1a1a1a;
    color: #ffffff;
    padding: 0.5rem;
  }

  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

    + .select-glow {
      opacity: 1;
    }
  }

  &.success {
    border-color: #4ade80;
    box-shadow: 0 0 0 1px rgba(74, 222, 128, 0.3), 0 4px 15px rgba(74, 222, 128, 0.1);
  }

  &.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3), 0 4px 15px rgba(239, 68, 68, 0.1);
  }
}

.select-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 14px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.optional {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
  margin-left: 2px;
}

// Animations
@keyframes stepSlideIn {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// Responsive Design - Tablet and Mobile
@media (max-width: 768px) {
  .auth-container-inner {
    max-width: 100%;
    padding: 0 1rem;
  }

  .header {
    padding: 1.5rem 2rem;
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .back-button {
    padding: 1rem 1.5rem;
    min-height: 44px; // Touch-friendly minimum
  }

  .step-indicators {
    gap: 0.8rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .step-indicator {
    min-width: 80px; // Ensure touch-friendly area
    padding: 0.8rem 0.5rem; // Increase touch area
    min-height: 60px; // Ensure adequate touch target
    position: relative;
    z-index: 10; // Ensure clickable
  }

  .step-number {
    width: 48px; // Larger touch-friendly target
    height: 48px;
    font-size: 0.9rem;
    position: relative;
    z-index: 2;
  }

  .step-label {
    font-size: 0.75rem;
    text-align: center;
  }

  .step-title {
    font-size: 1.8rem;
  }

  .step-description {
    font-size: 0.95rem;
    padding: 0 1rem;
  }

  .form-grid {
    gap: 1.5rem;
  }

  .form-input, .form-select {
    padding: 1.3rem 1.5rem; // Larger touch area
    font-size: 16px; // Prevent zoom on iOS (must be exactly 16px)
    min-height: 52px; // Ensure adequate touch target
    position: relative;
    z-index: 5; // Ensure proper layering
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .social-buttons {
    gap: 1rem;
  }

  .social-btn {
    padding: 1.3rem 1.5rem; // Touch-friendly
    min-height: 52px; // Larger touch target
    position: relative;
    z-index: 10; // Ensure clickable
    border: none; // Remove border conflicts
    background-clip: padding-box;
    touch-action: manipulation; // Optimize for touch
  }

  .step-navigation {
    flex-direction: column;
    gap: 1.2rem;
    padding: 1rem 0;
    justify-content: center;
    align-items: center;

    .nav-button {
      width: 100%;
      min-width: auto;
      min-height: 52px; // Larger touch-friendly target
      padding: 1.3rem 2rem;
      position: relative;

      // Reset positioning for mobile - stack vertically
      &.secondary, &.primary {
        position: relative;
        left: auto;
        transform: none;
      }
      z-index: 10; // Ensure clickable
      border: none; // Remove any border conflicts
      background-clip: padding-box; // Ensure proper background
    }
  }

  .otp-section {
    padding: 2rem 1.5rem;
  }

  .otp-input-container {
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .otp-digit-input {
    width: 52px; // Larger touch-friendly target
    height: 52px;
    font-size: 1.4rem;
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .checkbox-custom {
    width: 28px; // Larger touch target
    height: 28px;
    position: relative;
    z-index: 10; // Ensure clickable
  }

  .checkbox-label {
    gap: 1rem;
    padding: 0.8rem 0; // More touch area
    min-height: 44px; // Ensure adequate touch target
    display: flex;
    align-items: center;
    position: relative;
    z-index: 5;
    touch-action: manipulation; // Optimize for touch
  }
}

// Mobile Optimization - Small screens (320px-480px)
@media (max-width: 480px) {
  .auth-section {
    padding: 2rem 1rem; // Match home page mobile spacing pattern
  }

  .auth-container-inner {
    padding: 0 0.5rem;
  }

  .header {
    padding: 1rem 1.5rem;
  }

  .step-progress {
    margin-bottom: 1.5rem;
  }

  .step-indicators {
    gap: 0.5rem;
    padding: 0 0.5rem;
  }

  .step-indicator {
    min-width: 70px;
    padding: 0.3rem;
  }

  .step-number {
    width: 40px; // Still touch-friendly
    height: 40px;
    font-size: 0.8rem;
  }

  .step-label {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .step-title {
    font-size: 1.6rem;
    line-height: 1.3;
  }

  .step-description {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }

  .auth-form {
    padding: 2rem 1.5rem;
  }

  .form-grid {
    gap: 1.3rem;
  }

  .form-input, .form-select {
    padding: 1.2rem 1.3rem; // Maintain touch-friendly size
    font-size: 16px; // Prevent zoom on iOS (must be exactly 16px)
    min-height: 50px; // Ensure adequate touch target
    position: relative;
    z-index: 5; // Ensure proper layering
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .social-btn {
    padding: 1.1rem 1.3rem;
    min-height: 44px; // Maintain touch target
    font-size: 0.9rem;
  }

  .nav-button {
    padding: 1.1rem 1.8rem;
    font-size: 0.95rem;
    min-height: 44px; // Touch-friendly
  }

  .otp-section {
    padding: 1.5rem 1rem;
  }

  .otp-input-container {
    gap: 0.6rem;
    max-width: 100%;
    overflow-x: auto;
    padding: 0.5rem 0;
  }

  .otp-digit-input {
    width: 48px; // Maintain touch-friendly size
    height: 48px;
    font-size: 1.3rem;
    flex-shrink: 0;
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .checkbox-custom {
    width: 22px;
    height: 22px;
  }

  .checkbox-label {
    font-size: 0.85rem;
    gap: 0.8rem;
    padding: 0.8rem 0; // Larger touch area
  }

  .checkbox-text {
    line-height: 1.4;
  }
}

// Extra small screens (320px and below)
@media (max-width: 320px) {
  .auth-container-inner {
    padding: 0 0.25rem;
  }

  .auth-form {
    padding: 1.5rem 1rem;
  }

  .step-indicators {
    gap: 0.3rem;
    padding: 0 0.25rem;
  }

  .step-indicator {
    min-width: 60px;
  }

  .step-number {
    width: 36px;
    height: 36px;
    font-size: 0.75rem;
  }

  .step-label {
    font-size: 0.65rem;
  }

  .otp-input-container {
    gap: 0.4rem;
  }

  .otp-digit-input {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

// Performance optimizations
.auth-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.auth-container-inner, .form-input, .form-select, .social-btn, .nav-button {
  will-change: transform, opacity;
  transform: translateZ(0);
}

.otp-digit-input {
  will-change: transform, border-color;
  transform: translateZ(0);
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .nav-button:hover,
  .social-btn:hover,
  .form-input:hover,
  .form-select:hover {
    transform: none; // Disable hover transforms on touch devices
  }

  .step-indicator:hover {
    .step-number {
      transform: none;
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .step-content {
    animation: none !important;
  }

  .geometric-bg {
    animation: none !important;

    .circle {
      animation: none !important;
    }

    .grid-pattern {
      animation: none !important;
    }
  }

  .corner-element {
    animation: none !important;
  }
}

// Landscape orientation support
@media screen and (orientation: landscape) and (max-height: 500px) {
  .auth-section {
    padding: 1rem 2rem;
  }

  .auth-header {
    margin-bottom: 1.5rem;
  }

  .step-progress {
    margin-bottom: 1rem;
  }

  .step-title {
    font-size: 1.4rem;
  }

  .step-description {
    font-size: 0.85rem;
  }

  .auth-form {
    padding: 1.5rem 2rem;
  }

  .form-grid {
    gap: 1rem;
  }

  .otp-section {
    padding: 1rem;
  }

  .step-navigation {
    margin: 1rem 0;
    padding-top: 1rem;

    // Ensure proper centering on small screens
    .nav-button {
      &.secondary, &.primary {
        position: relative;
        left: auto;
        transform: none;
      }
    }
  }

  .nav-button {
    padding: 0.8rem 1.5rem;
    min-height: 40px;
  }
}