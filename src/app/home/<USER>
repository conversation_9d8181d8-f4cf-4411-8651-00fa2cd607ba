// Base container and background
.home-container {
  min-height: 100vh;
  background:
    radial-gradient(ellipse at 20% 80%, rgba(15, 15, 15, 0.8) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(10, 10, 10, 0.6) 0%, transparent 50%),
    radial-gradient(ellipse at center, #0a0a0a 0%, #000000 70%);
  color: #ffffff;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  /* overflow-x: hidden; */
  scroll-behavior: smooth;

  // Enhanced background texture
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
  }
}

// Enhanced background geometric elements
.geometric-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.circle {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  animation: circleFloat 25s ease-in-out infinite;

  // Enhanced glow effect
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 25%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 75%,
      rgba(255, 255, 255, 0.1) 100%);
    animation: circleGlow 8s ease-in-out infinite;
    opacity: 0;
  }

  &.circle-1 {
    width: 400px;
    height: 400px;
    top: 5%;
    left: -8%;
    animation-delay: 0s;

    &::before {
      animation-delay: 0s;
    }
  }

  &.circle-2 {
    width: 250px;
    height: 250px;
    top: 45%;
    right: -5%;
    animation-delay: 8s;

    &::before {
      animation-delay: 2s;
    }
  }

  &.circle-3 {
    width: 180px;
    height: 180px;
    bottom: 15%;
    left: 10%;
    animation-delay: 16s;

    &::before {
      animation-delay: 4s;
    }
  }

  // Additional circles for more depth
  &.circle-4 {
    width: 120px;
    height: 120px;
    top: 70%;
    right: 20%;
    animation-delay: 12s;
    border-color: rgba(255, 255, 255, 0.03);

    &::before {
      animation-delay: 6s;
    }
  }

  &.circle-5 {
    width: 80px;
    height: 80px;
    top: 25%;
    left: 60%;
    animation-delay: 20s;
    border-color: rgba(255, 255, 255, 0.04);

    &::before {
      animation-delay: 1s;
    }
  }
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.01) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.01) 1px, transparent 1px);
  background-size: 60px 60px, 60px 60px, 120px 120px, 80px 80px;
  animation: gridShift 40s linear infinite;
  opacity: 0.6;
}

// Enhanced floating particles
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 70%, transparent 100%);
  border-radius: 50%;
  animation: particleFloat 15s ease-in-out infinite;

  &.particle-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 18s;
  }

  &.particle-2 {
    top: 60%;
    left: 80%;
    animation-delay: 3s;
    animation-duration: 22s;
  }

  &.particle-3 {
    top: 40%;
    left: 30%;
    animation-delay: 6s;
    animation-duration: 16s;
  }

  &.particle-4 {
    top: 80%;
    left: 70%;
    animation-delay: 9s;
    animation-duration: 20s;
  }

  &.particle-5 {
    top: 10%;
    left: 60%;
    animation-delay: 12s;
    animation-duration: 14s;
  }

  &.particle-6 {
    top: 70%;
    left: 20%;
    animation-delay: 15s;
    animation-duration: 24s;
  }
}

// Corner decorative elements
.corner-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.corner-element {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 1px solid rgba(255, 255, 255, 0.08);

  &.top-left {
    top: 40px;
    left: 40px;
    border-right: none;
    border-bottom: none;
    animation: cornerPulse 6s ease-in-out infinite;
  }

  &.top-right {
    top: 40px;
    right: 40px;
    border-left: none;
    border-bottom: none;
    animation: cornerPulse 6s ease-in-out infinite 1.5s;
  }

  &.bottom-left {
    bottom: 40px;
    left: 40px;
    border-right: none;
    border-top: none;
    animation: cornerPulse 6s ease-in-out infinite 3s;
  }

  &.bottom-right {
    bottom: 40px;
    right: 40px;
    border-left: none;
    border-top: none;
    animation: cornerPulse 6s ease-in-out infinite 4.5s;
  }
}

// Header styling
.header {
  padding: 2rem 3rem;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;

  // Ensure header doesn't break on mobile
  @media (max-width: 768px) {
    flex-wrap: nowrap;
    overflow: visible;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

// Header navigation
.header-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  position: relative;
  padding: 0.8rem 1.8rem;
  border-radius: 50px;
  text-decoration: none;
  font-family: inherit;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  cursor: pointer;
  pointer-events: auto !important;
  z-index: 9999 !important; // Maximum z-index to ensure always clickable
  position: relative;
  isolation: isolate; // Create new stacking context

  // Default (Login) styling - Completely transparent with visible border
  background: transparent !important;
  border: 2px solid rgba(255, 255, 255, 0.5); // Thicker, more visible border
  color: rgba(255, 255, 255, 0.95);

  // Enhanced mobile visibility
  @media (max-width: 768px) {
    border: 2px solid rgba(255, 255, 255, 0.7); // Even more visible on mobile
    min-height: 48px; // Ensure touch target
    font-weight: 600; // Bolder text for better visibility
  }

  // Primary (Register) styling
  &.primary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
  }

  &:hover {
    transform: translateY(-2px);

    &:not(.primary) {
      background: transparent !important; // Keep transparent on hover
      border-color: rgba(255, 255, 255, 0.9); // Brighter border on hover
      color: #ffffff;
      box-shadow: 0 4px 20px rgba(255, 255, 255, 0.15); // Subtle glow effect
    }

    &.primary {
      background: transparent !important; // Keep transparent on hover
      border-color: rgba(255, 255, 255, 0.9); // Brighter border on hover
      box-shadow: 0 4px 20px rgba(255, 255, 255, 0.15); // Subtle glow effect
    }

    .nav-glow {
      opacity: 1;
    }
  }

  // Ensure clickability on all devices
  &:active, &:focus {
    outline: none;
    background: transparent !important; // Keep transparent on active/focus
    border-color: rgba(255, 255, 255, 0.8);
    color: #ffffff;
  }
}

.nav-text {
  position: relative;
  z-index: 2;
}

.nav-glow {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.logo-wrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 4s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.header-logo {
  width: 45px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 15px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 30px rgba(255, 255, 255, 0.2));
  transition: filter 0.3s ease;

  &:hover {
    filter:
      brightness(0)
      invert(1)
      drop-shadow(0 0 20px rgba(255, 255, 255, 0.6))
      drop-shadow(0 0 40px rgba(255, 255, 255, 0.3));
  }
}

.app-title {
  font-size: 1.8rem;
  font-weight: 200;
  margin: 0;
  letter-spacing: 0.08em;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

// Enhanced hero section
.hero-section {
  padding: 2rem 3rem 2rem 3rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  align-items: center;
  max-width: 1500px;
  margin: 0 auto;
  position: relative;
  z-index: 10;

  // Hero section background enhancement
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    transform: translate(-50%, -50%);
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.02) 0%, transparent 70%);
    border-radius: 50%;
    animation: heroGlow 8s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.hero-content {
  animation: fadeInUp 1.5s ease-out forwards;
  opacity: 1; // Default to visible, animation will override
  transform: translateY(0); // Default position
  position: relative;

  // Ensure content is visible if animation fails
  &.no-animation {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  // Content enhancement glow
  &::before {
    content: '';
    position: absolute;
    top: -20%;
    left: -10%;
    width: 120%;
    height: 140%;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.01) 0%, transparent 60%);
    border-radius: 50%;
    animation: contentGlow 6s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.hero-badge {
  display: inline-block;
  margin-bottom: 2.5rem;
  padding: 1rem 2.5rem;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 50px;
  backdrop-filter: blur(15px);
  animation: badgeGlow 4s ease-in-out infinite alternate;
  position: relative;
  overflow: hidden;

  // Badge shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: badgeShimmer 3s ease-in-out infinite;
  }
}

.badge-text {
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
}

.hero-headline {
  font-size: 4rem;
  font-weight: 200;
  line-height: 1.15;
  margin: 0 0 3rem 0;
  letter-spacing: -0.03em;
  position: relative;
}

.headline-text {
  background: linear-gradient(135deg,
    #ffffff 0%,
    #f0f0f0 25%,
    #ffffff 50%,
    #e8e8e8 75%,
    #ffffff 100%);
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.4);
  animation: textShimmer 6s ease-in-out infinite;
  position: relative;

  // Enhanced text glow
  &::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: blur(1px);
    opacity: 0.3;
    z-index: -1;
  }
}

.hero-description {
  margin-bottom: 4rem;
  max-width: 650px;
  position: relative;
}

.description-text {
  font-size: 1.3rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 2rem;
  font-weight: 300;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);

  // Enhanced readability
  &::first-letter {
    font-size: 1.5em;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.95);
  }
}

.description-highlight {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
  margin: 0;
  position: relative;

  // Subtle underline effect
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
  }
}

.coming-soon {
  color: #ffffff;
  font-weight: 600;
  text-shadow:
    0 0 20px rgba(255, 255, 255, 0.6),
    0 0 40px rgba(255, 255, 255, 0.3);
  animation: comingSoonPulse 3s ease-in-out infinite;
  position: relative;

  // Enhanced glow effect
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -4px;
    right: -4px;
    bottom: -2px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 4px;
    animation: comingSoonGlow 3s ease-in-out infinite;
    z-index: -1;
  }
}

.hero-actions {
  display: flex;
  gap: 2rem;
  align-items: center;
  margin-top: 1rem;
}

.cta-button {
  position: relative;
  padding: 1.3rem 3rem;
  border: none;
  border-radius: 50px;
  font-family: inherit;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;

  &.primary {
    background:
      linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%),
      linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    border: 1px solid rgba(255, 255, 255, 0.35);
    color: #ffffff;
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Enhanced button shimmer
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: buttonShimmer 3s ease-in-out infinite;
    }

    &:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow:
        0 15px 40px rgba(255, 255, 255, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);

      .button-glow {
        opacity: 1;
        transform: scale(1.1);
      }

      &::before {
        animation-duration: 1.5s;
      }
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
    }
  }

  &.secondary {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.4);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
  }
}

.button-text {
  position: relative;
  z-index: 3;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.button-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%),
    radial-gradient(ellipse at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  border-radius: 52px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: blur(1px);
  z-index: 1;
}

// Hero visual section
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 1.2s ease-out 0.3s forwards;
  opacity: 0;
  transform: translateX(30px);
}

.visual-container {
  position: relative;
  width: 400px;
  height: 400px;
}

.ai-orb {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
}

.orb-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 70%, transparent 100%);
  border-radius: 50%;
  animation: orbPulse 3s ease-in-out infinite;
  box-shadow:
    0 0 30px rgba(255, 255, 255, 0.6),
    0 0 60px rgba(255, 255, 255, 0.3),
    0 0 90px rgba(255, 255, 255, 0.1);
}

.orb-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: orbRotate 10s linear infinite;

  &.ring-1 {
    width: 120px;
    height: 120px;
    animation-duration: 8s;
  }

  &.ring-2 {
    width: 160px;
    height: 160px;
    animation-duration: 12s;
    animation-direction: reverse;
  }

  &.ring-3 {
    width: 200px;
    height: 200px;
    animation-duration: 15s;
  }
}

// Enhanced content section
.content-section {
  padding: 2rem 3rem 2rem 3rem;
  max-width: 1300px;
  margin: 0 auto;
  position: relative;
  z-index: 10;

  // Section background enhancement
  &::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 50%;
    width: 80%;
    height: 60%;
    transform: translateX(-50%);
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.01) 0%, transparent 70%);
    border-radius: 50%;
    animation: sectionGlow 10s ease-in-out infinite alternate;
  }
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 4rem;
  animation: fadeInUp 1.5s ease-out 0.8s forwards;
  opacity: 1; // Default to visible
  transform: translateY(0); // Default position
  position: relative;

  // Ensure content is visible if animation fails
  &.no-animation {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

// Minimalist futuristic feature cards
.feature-card {
  padding: 3.5rem 2.5rem;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.01) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  text-align: center;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;

  // Minimalist border enhancement
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    padding: 1px;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 25%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 75%,
      rgba(255, 255, 255, 0.1) 100%);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  // Subtle shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.03), transparent);
    animation: cardShimmer 6s ease-in-out infinite;
  }

  &:hover {
    transform: translateY(-12px);
    border-color: rgba(255, 255, 255, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.05);

    &::after {
      opacity: 1;
    }

    &::before {
      animation-duration: 3s;
    }

    .card-icon-container {
      transform: translateY(-8px);
    }
  }

  // Staggered animation delays
  &:nth-child(1) {
    animation-delay: 0.2s;
  }

  &:nth-child(2) {
    animation-delay: 0.4s;
  }

  &:nth-child(3) {
    animation-delay: 0.6s;
  }
}

// Futuristic geometric icon containers
.card-icon-container {
  margin-bottom: 2.5rem;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: iconFloat 6s ease-in-out infinite;
}

.card-icon {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Advanced Reasoning Icon (Brain-like structure with layered thinking)
.advanced-reasoning {
  .brain-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 70%, transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      inset 0 0 10px rgba(255, 255, 255, 0.3);
    animation: brainCorePulse 3s ease-in-out infinite;
  }

  .thought-layer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    &.layer-1 {
      width: 35px;
      height: 35px;
      animation: thoughtRotate 8s linear infinite;
    }

    &.layer-2 {
      width: 45px;
      height: 45px;
      animation: thoughtRotate 12s linear infinite reverse;
    }

    &.layer-3 {
      width: 55px;
      height: 55px;
      animation: thoughtRotate 16s linear infinite;
    }
  }

  .synapse {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
    animation: synapseFire 2s ease-in-out infinite;

    &.synapse-1 {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      animation-delay: 0s;
    }

    &.synapse-2 {
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      animation-delay: 0.3s;
    }

    &.synapse-3 {
      bottom: 0;
      left: 25%;
      animation-delay: 0.6s;
    }

    &.synapse-4 {
      top: 25%;
      left: 0;
      animation-delay: 0.9s;
    }

    &.synapse-5 {
      bottom: 25%;
      right: 25%;
      animation-delay: 1.2s;
    }

    &.synapse-6 {
      top: 0;
      right: 25%;
      animation-delay: 1.5s;
    }
  }

  .reasoning-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: reasoningExpand 4s ease-out infinite;

    &.pulse-1 {
      width: 25px;
      height: 25px;
      animation-delay: 0s;
    }

    &.pulse-2 {
      width: 35px;
      height: 35px;
      animation-delay: 1.3s;
    }

    &.pulse-3 {
      width: 45px;
      height: 45px;
      animation-delay: 2.6s;
    }
  }
}

// Predictive Intelligence Icon (Crystal ball with future timeline)
.predictive-intelligence {
  .crystal-ball {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.4),
      inset 0 0 15px rgba(255, 255, 255, 0.2);
    animation: crystalGlow 3s ease-in-out infinite;

    .inner-sphere {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 12px;
      height: 12px;
      transform: translate(-50%, -50%);
      background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.3) 100%);
      border-radius: 50%;
      animation: innerVision 2s ease-in-out infinite;
    }

    .prediction-wave {
      position: absolute;
      top: 50%;
      left: 50%;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: predictionRipple 3s ease-out infinite;

      &.wave-1 {
        width: 35px;
        height: 35px;
        animation-delay: 0s;
      }

      &.wave-2 {
        width: 45px;
        height: 45px;
        animation-delay: 1s;
      }

      &.wave-3 {
        width: 55px;
        height: 55px;
        animation-delay: 2s;
      }
    }
  }

  .future-timeline {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 8px;

    .timeline-connector {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
      transform: translateY(-50%);
      animation: timelineFlow 4s ease-in-out infinite;
    }

    .timeline-point {
      position: absolute;
      top: 50%;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      transform: translateY(-50%);

      &.past {
        left: 0;
        background: rgba(255, 255, 255, 0.5);
        animation: pastPulse 3s ease-in-out infinite;
      }

      &.present {
        left: 20px;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
        animation: presentPulse 2s ease-in-out infinite;
      }

      &.future {
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
        animation: futurePulse 1.5s ease-in-out infinite;

        &.future-1 {
          right: 15px;
          animation-delay: 0.5s;
        }

        &.future-2 {
          right: 0;
          animation-delay: 1s;
        }
      }
    }
  }

  .insight-beam {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: insightSweep 4s ease-in-out infinite;

    &.beam-1 {
      top: 20%;
      left: 10%;
      width: 15px;
      height: 1px;
      transform: rotate(30deg);
      animation-delay: 0s;
    }

    &.beam-2 {
      top: 60%;
      right: 15%;
      width: 12px;
      height: 1px;
      transform: rotate(-45deg);
      animation-delay: 1.3s;
    }

    &.beam-3 {
      bottom: 25%;
      left: 20%;
      width: 10px;
      height: 1px;
      transform: rotate(60deg);
      animation-delay: 2.6s;
    }
  }
}

// Precision Target Icon
.precision-target {
  .target-ring {
    position: absolute;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: targetPulse 3s ease-in-out infinite;

    &.ring-1 {
      width: 50px;
      height: 50px;
      animation-delay: 0s;
    }

    &.ring-2 {
      width: 35px;
      height: 35px;
      animation-delay: 0.5s;
    }

    &.ring-3 {
      width: 20px;
      height: 20px;
      animation-delay: 1s;
    }
  }

  .target-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    animation: centerPulse 2s ease-in-out infinite;
  }

  .precision-beam {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: beamSweep 4s ease-in-out infinite;

    &.beam-1 {
      top: 50%;
      left: 0;
      width: 100%;
      height: 1px;
      transform: translateY(-50%);
      animation-delay: 0s;
    }

    &.beam-2 {
      top: 0;
      left: 50%;
      width: 1px;
      height: 100%;
      transform: translateX(-50%);
      animation-delay: 2s;
    }
  }
}

// Minimalist card content styling
.card-title {
  font-size: 1.4rem;
  font-weight: 300;
  margin-bottom: 1.2rem;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 0.03em;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  position: relative;

  // Subtle minimalist underline
  &::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    width: 40px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-50%);
    opacity: 0.6;
  }
}

.card-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 300;
  max-width: 280px;
  margin: 0 auto;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.05);
}

// Footer
.footer {
  padding: 4rem 3rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 10;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-logo-img {
  width: 35px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.footer-brand {
  font-size: 1.2rem;
  font-weight: 300;
  letter-spacing: 0.05em;
  color: rgba(255, 255, 255, 0.9);
}

.footer-text p {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 300;
}

// Animations
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes circleFloat {
  0%, 100% {
    opacity: 0.03;
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    opacity: 0.08;
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes gridShift {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes cornerPulse {
  0%, 100% {
    opacity: 0.08;
    transform: scale(1);
  }
  50% {
    opacity: 0.15;
    transform: scale(1.05);
  }
}

@keyframes logoGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes badgeGlow {
  0% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

@keyframes textShimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes comingSoonPulse {
  0%, 100% {
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 25px rgba(255, 255, 255, 0.8);
  }
}

@keyframes orbPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
}

@keyframes orbRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

// Enhanced animations
@keyframes circleGlow {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0px) translateX(0px);
  }
  25% {
    opacity: 0.8;
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-40px) translateX(-5px);
  }
  75% {
    opacity: 0.9;
    transform: translateY(-20px) translateX(-10px);
  }
}

@keyframes heroGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes contentGlow {
  0% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

@keyframes badgeShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes buttonShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes comingSoonGlow {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes sectionGlow {
  0% {
    opacity: 0.3;
    transform: translateX(-50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translateX(-50%) scale(1.1);
  }
}

@keyframes cardShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes iconGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// Advanced Reasoning animations
@keyframes brainCorePulse {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      inset 0 0 10px rgba(255, 255, 255, 0.3);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow:
      0 0 30px rgba(255, 255, 255, 1),
      inset 0 0 15px rgba(255, 255, 255, 0.5);
  }
}

@keyframes thoughtRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes synapseFire {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
    box-shadow: 0 0 15px rgba(255, 255, 255, 1);
  }
}

@keyframes reasoningExpand {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

// Predictive Intelligence animations
@keyframes crystalGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.4),
      inset 0 0 15px rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
  }
  50% {
    box-shadow:
      0 0 30px rgba(255, 255, 255, 0.7),
      inset 0 0 20px rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.9);
  }
}

@keyframes innerVision {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes predictionRipple {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

@keyframes timelineFlow {
  0%, 100% {
    opacity: 0.6;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
  }
  50% {
    opacity: 1;
    background: linear-gradient(90deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.6));
  }
}

@keyframes pastPulse {
  0%, 100% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.2);
  }
}

@keyframes presentPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.3);
    box-shadow: 0 0 15px rgba(255, 255, 255, 1);
  }
}

@keyframes futurePulse {
  0%, 100% {
    opacity: 0.9;
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.4);
    box-shadow: 0 0 20px rgba(255, 255, 255, 1);
  }
}

@keyframes insightSweep {
  0%, 100% {
    opacity: 0.3;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  }
  50% {
    opacity: 0.8;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 1), transparent);
  }
}

@keyframes targetPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
    border-color: rgba(255, 255, 255, 0.6);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
    border-color: rgba(255, 255, 255, 0.9);
  }
}

@keyframes centerPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 25px rgba(255, 255, 255, 1);
  }
}

@keyframes beamSweep {
  0%, 100% {
    opacity: 0.3;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }
  50% {
    opacity: 0.8;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 1), transparent);
  }
}

// Responsive Design
// Tablet and Mobile Optimization
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem 2rem;
  }

  .header-nav {
    gap: 1.2rem;
    justify-content: center;
  }

  .nav-link {
    padding: 1.2rem 1.8rem; // Touch-friendly
    min-height: 48px; // Larger touch target
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
    text-decoration: none; // Ensure no text decoration issues
  }

  .hero-section {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
    padding: 3rem 2rem;
  }

  .hero-headline {
    font-size: 3rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 1.1rem;
    padding: 0 1rem;
  }

  .cta-button {
    padding: 1.3rem 2.5rem; // Touch-friendly
    min-height: 52px; // Larger touch target
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .visual-container {
    width: 300px;
    height: 300px;
  }

  .ai-orb {
    width: 150px;
    height: 150px;
  }

  .orb-core {
    width: 60px;
    height: 60px;
  }

  .orb-ring {
    &.ring-1 {
      width: 90px;
      height: 90px;
    }

    &.ring-2 {
      width: 120px;
      height: 120px;
    }

    &.ring-3 {
      width: 150px;
      height: 150px;
    }
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem 1.5rem;
    flex-direction: row; // Keep horizontal layout for mobile
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .logo-section {
    flex-shrink: 0; // Prevent logo from shrinking
  }

  .app-title {
    font-size: 1.2rem; // Smaller title on mobile
  }

  .header-nav {
    gap: 0.8rem;
    flex-wrap: nowrap; // Keep buttons in one line
    justify-content: flex-end;
    flex-shrink: 0; // Prevent nav from shrinking
  }

  .nav-link {
    padding: 1rem 1.5rem; // Larger touch area for mobile
    min-width: 100px; // Adequate touch-friendly width
    min-height: 48px; // Touch-friendly height (above 44px minimum)
    position: relative;
    z-index: 9999 !important; // Maximum z-index to ensure clickable
    touch-action: manipulation; // Optimize for touch
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem; // Readable text for mobile
    border: 2px solid rgba(255, 255, 255, 0.7) !important; // Thicker, more visible border
    background: transparent !important; // No background
    cursor: pointer;
    text-decoration: none;
    pointer-events: auto !important;
    isolation: isolate; // Create new stacking context

    // Enhanced mobile interaction
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      transform: none; // Disable transform on mobile
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background
      border-color: rgba(255, 255, 255, 0.7) !important;

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
        border-color: rgba(255, 255, 255, 0.95) !important;
      }
    }
  }

  .hero-section {
    padding: 3rem 1.5rem;
  }

  .hero-headline {
    font-size: 2.5rem;
    line-height: 1.3;
  }

  .description-text {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
    padding: 1.1rem 2rem; // Touch-friendly
    min-height: 48px;
  }

  .content-section {
    padding: 4rem 1.5rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }

  .footer {
    padding: 3rem 1.5rem 1.5rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .corner-element {
    width: 60px;
    height: 60px;

    &.top-left, &.top-right {
      top: 20px;
    }

    &.bottom-left, &.bottom-right {
      bottom: 20px;
    }

    &.top-left, &.bottom-left {
      left: 20px;
    }

    &.top-right, &.bottom-right {
      right: 20px;
    }
  }
}

// Small Mobile Optimization
@media (max-width: 480px) {
  .header {
    padding: 0.8rem 1rem;
    flex-direction: row; // Keep horizontal
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    gap: 0.8rem; // Reduce gap between logo and title
  }

  .app-title {
    font-size: 1rem; // Even smaller on small screens
  }

  .header-nav {
    gap: 0.5rem; // Tighter spacing
  }

  .nav-link {
    padding: 0.9rem 1.2rem; // Adequate touch area
    min-width: 90px; // Touch-friendly width
    min-height: 44px; // Minimum touch target
    font-size: 0.85rem;
    position: relative;
    z-index: 9999 !important; // Maximum z-index
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
    background: transparent !important; // No background
    pointer-events: auto !important;
    isolation: isolate;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
      }
    }
  }

  .hero-section {
    padding: 2rem 1rem;
  }

  .hero-headline {
    font-size: 2rem;
    line-height: 1.2;
  }

  .description-text {
    font-size: 1rem;
    padding: 0 0.5rem;
  }

  .cta-button {
    padding: 1rem 1.8rem;
    font-size: 0.95rem;
    min-height: 44px; // Maintain touch-friendly size
  }

  .content-section {
    padding: 3rem 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 0.95rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }

  .card-icon-container {
    height: 60px;
    margin-bottom: 1.5rem;
  }

  .card-icon {
    width: 45px;
    height: 45px;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-description {
    font-size: 0.9rem;
  }

  .footer {
    padding: 2rem 1rem 1rem;
  }
}

// Extra Small Mobile
@media (max-width: 320px) {
  .header {
    padding: 0.6rem 0.8rem;
    flex-direction: row; // Keep horizontal
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    gap: 0.5rem;
  }

  .app-title {
    font-size: 0.9rem; // Very compact
  }

  .header-nav {
    gap: 0.3rem; // Very tight spacing
  }

  .nav-link {
    padding: 0.8rem 1rem; // Maintain touch-friendly size
    min-width: 80px; // Adequate touch target even on small screens
    min-height: 44px; // Maintain minimum touch target
    font-size: 0.8rem;
    position: relative;
    z-index: 9999 !important; // Maximum z-index
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
    background: transparent !important; // No background
    pointer-events: auto !important;
    isolation: isolate;

    &:hover, &:active, &:focus, &:visited {
      background: transparent !important; // Keep transparent
      border-color: rgba(255, 255, 255, 0.95) !important;
      color: #ffffff !important;
      outline: none;
    }

    &.primary {
      background: transparent !important; // No background

      &:hover, &:active, &:focus, &:visited {
        background: transparent !important; // Keep transparent
      }
    }
  }

  .hero-section {
    padding: 1.5rem 0.8rem;
  }

  .hero-headline {
    font-size: 1.8rem;
  }

  .cta-button {
    padding: 0.9rem 1.5rem;
    font-size: 0.9rem;
    min-height: 40px;
  }

  .content-section {
    padding: 2.5rem 0.8rem;
  }

  .feature-card {
    padding: 1.5rem 1.2rem;
  }
}

// Performance optimizations
.home-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.feature-card, .cta-button, .nav-link {
  will-change: transform, opacity;
  transform: translateZ(0);
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .feature-card:hover,
  .cta-button:hover,
  .nav-link:hover {
    transform: none; // Disable hover transforms on touch devices
  }

  .card-icon:hover {
    transform: none;
  }

  // Ensure nav links are clickable on touch devices
  .nav-link {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 9999 !important;
    isolation: isolate;

    // Enhanced touch feedback
    &:active, &:focus {
      background: transparent !important; // Keep transparent on touch
      border-color: rgba(255, 255, 255, 1) !important; // Full opacity border
      color: #ffffff !important;
      transform: scale(0.98); // Subtle feedback instead of background
      outline: none;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3); // Glow effect for feedback
    }

    // Ensure clickability on all touch events
    &:hover, &:focus-visible {
      border-color: rgba(255, 255, 255, 0.9) !important;
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  // Ensure content is visible when animations are disabled
  .hero-content,
  .content-grid {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .geometric-bg {
    animation: none !important;

    .circle {
      animation: none !important;
    }

    .grid-pattern {
      animation: none !important;
    }
  }

  .corner-element {
    animation: none !important;
  }

  .ai-orb {
    animation: none !important;

    .orb-core {
      animation: none !important;
    }

    .orb-ring {
      animation: none !important;
    }
  }

  .card-icon {
    animation: none !important;
  }
}

@media (max-width: 480px) {
  .hero-headline {
    font-size: 2rem;
  }

  .description-text {
    font-size: 1rem;
  }

  .hero-badge {
    padding: 0.6rem 1.5rem;
  }

  .badge-text {
    font-size: 0.8rem;
  }

  .visual-container {
    width: 250px;
    height: 250px;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .corner-element {
    display: none; // Hide on very small screens
  }
}

// Performance optimizations
.home-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.hero-content, .hero-visual, .feature-card {
  will-change: transform, opacity;
  transform: translateZ(0);
}

// Additional reduced motion support (duplicate section removed)
// Content visibility is handled in the main reduced motion section above