import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { SharedHeaderComponent } from '../components/shared-header/shared-header.component';

@Component({
  selector: 'app-home',
  imports: [RouterModule, SharedHeaderComponent],
  templateUrl: './home.html',
  styleUrl: './home.scss'
})
export class Home {
  constructor(private router: Router) {}

  navigateToWishlist(): void {
    this.router.navigate(['/wishlist']);
  }

  navigateToLogin(): void {
    this.router.navigate(['/login']);
  }

  navigateToRegister(): void {
    this.router.navigate(['/register']);
  }

  navigateToAccount(): void {
    this.router.navigate(['/login']);
  }
}
