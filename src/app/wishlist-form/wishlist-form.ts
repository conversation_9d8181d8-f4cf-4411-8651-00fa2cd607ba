import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SharedHeaderComponent } from '../components/shared-header/shared-header.component';

@Component({
  selector: 'app-wishlist-form',
  imports: [ReactiveFormsModule, CommonModule, SharedHeaderComponent],
  templateUrl: './wishlist-form.html',
  styleUrl: './wishlist-form.scss'
})
export class WishlistForm implements OnInit {
  wishlistForm!: FormGroup;
  isLoading = false;
  isSubmitted = false;
  hasError = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.wishlistForm = this.formBuilder.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      company: [''],
      businessType: ['', [Validators.required]]
    });
  }

  navigateBack(): void {
    this.router.navigate(['/home']);
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.wishlistForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isFieldValid(fieldName: string): boolean {
    const field = this.wishlistForm.get(fieldName);
    return !!(field && field.valid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.wishlistForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      fullName: 'Full Name',
      email: 'Email Address',
      company: 'Company',
      businessType: 'Business Type'
    };
    return displayNames[fieldName] || fieldName;
  }

  onSubmit(): void {
    if (this.wishlistForm.valid) {
      this.isLoading = true;
      this.hasError = false;

      // Simulate API call
      setTimeout(() => {
        // Simulate random success/failure for demo
        const success = Math.random() > 0.1; // 90% success rate

        this.isLoading = false;
        if (success) {
          this.isSubmitted = true;
          console.log('Wishlist form submitted:', this.wishlistForm.value);
        } else {
          this.hasError = true;
        }
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.wishlistForm.controls).forEach(key => {
        this.wishlistForm.get(key)?.markAsTouched();
      });
    }
  }

  resetForm(): void {
    this.hasError = false;
    this.isSubmitted = false;
    this.isLoading = false;
    this.initializeForm();
  }
}
