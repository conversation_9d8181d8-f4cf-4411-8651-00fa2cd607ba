// Base container and background
.wishlist-container {
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 70%);
  color: #ffffff;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  position: relative;
  overflow-x: hidden;
}

// Background geometric elements (same as home page)
.geometric-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.03);
  border-radius: 50%;
  animation: circleFloat 20s ease-in-out infinite;

  &.circle-1 {
    width: 250px;
    height: 250px;
    top: 15%;
    left: -3%;
    animation-delay: 0s;
  }

  &.circle-2 {
    width: 180px;
    height: 180px;
    top: 60%;
    right: -2%;
    animation-delay: 7s;
  }

  &.circle-3 {
    width: 120px;
    height: 120px;
    bottom: 25%;
    left: 20%;
    animation-delay: 14s;
  }
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridShift 30s linear infinite;
}

// Corner decorative elements
.corner-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.corner-element {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(255, 255, 255, 0.08);

  &.top-left {
    top: 30px;
    left: 30px;
    border-right: none;
    border-bottom: none;
    animation: cornerPulse 6s ease-in-out infinite;
  }

  &.top-right {
    top: 30px;
    right: 30px;
    border-left: none;
    border-bottom: none;
    animation: cornerPulse 6s ease-in-out infinite 1.5s;
  }

  &.bottom-left {
    bottom: 30px;
    left: 30px;
    border-right: none;
    border-top: none;
    animation: cornerPulse 6s ease-in-out infinite 3s;
  }

  &.bottom-right {
    bottom: 30px;
    right: 30px;
    border-left: none;
    border-top: none;
    animation: cornerPulse 6s ease-in-out infinite 4.5s;
  }
}

// Header styling
.header {
  padding: 2rem 3rem;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.logo-wrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 4s ease-in-out infinite alternate;
    z-index: -1;
  }
}

.header-logo {
  width: 45px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 15px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 30px rgba(255, 255, 255, 0.2));
}

.app-title {
  font-size: 1.8rem;
  font-weight: 200;
  margin: 0;
  letter-spacing: 0.08em;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    transform: translateX(-2px);
  }
}

.back-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.back-button:hover .back-icon {
  transform: translateX(-2px);
}

// Form section - match home page hero-section spacing
.form-section {
  padding: 2rem 3rem 2rem 3rem; // Match home page hero-section spacing exactly
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  z-index: 10;
}

.form-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
  animation: fadeInUp 1.2s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.form-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 0 1rem;
}

.form-badge {
  display: inline-block;
  margin-bottom: 1.5rem;
  padding: 0.6rem 1.8rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  animation: badgeGlow 3s ease-in-out infinite alternate;
}

.badge-text {
  font-size: 0.85rem;
  font-weight: 400;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
}

.form-title {
  font-size: 3rem;
  font-weight: 200;
  line-height: 1.2;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.02em;
}

.title-text {
  background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 50%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
  animation: textShimmer 4s ease-in-out infinite;
}

.form-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 300;
  max-width: 500px;
  margin: 0 auto;
}

// Form styling
.wishlist-form {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem 2.5rem;
  backdrop-filter: blur(10px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  margin: 0 auto;
  position: relative;
}

.form-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.8rem;
  letter-spacing: 0.02em;
  text-align: left;
}

.required {
  color: #ff6b6b;
  margin-left: 2px;
}

.optional {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
  margin-left: 2px;
}

.input-wrapper, .select-wrapper {
  position: relative;
  width: 100%;
}

.form-input, .form-select {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 14px;
  color: #ffffff;
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
  box-sizing: border-box;

  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
    font-weight: 300;
  }

  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

    + .input-glow, + .select-glow {
      opacity: 1;
    }
  }

  &.success {
    border-color: #4ade80;
    box-shadow: 0 0 0 1px rgba(74, 222, 128, 0.3), 0 4px 15px rgba(74, 222, 128, 0.1);
  }

  &.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3), 0 4px 15px rgba(239, 68, 68, 0.1);
  }
}

.form-select {
  cursor: pointer;

  option {
    background: #1a1a1a;
    color: #ffffff;
  }
}

.input-glow, .select-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.error-message {
  margin-top: 0.6rem;
  font-size: 0.85rem;
  color: #ef4444;
  font-weight: 400;
  animation: errorSlideIn 0.3s ease-out;
  text-align: left;
  padding-left: 0.5rem;
}

// Form actions
.form-actions {
  text-align: center;
}

.submit-button {
  position: relative;
  padding: 1rem 2.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: #ffffff;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  min-width: 180px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.2);

    .button-glow {
      opacity: 1;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &.loading {
    pointer-events: none;
  }
}

.button-text {
  position: relative;
  z-index: 2;
}

.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  right: 1.5rem;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Success and error states
.success-container, .error-container {
  text-align: center;
  padding: 3rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  animation: fadeInScale 0.6s ease-out forwards;
}

.success-icon, .error-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: iconBounce 0.6s ease-out 0.2s forwards;
  opacity: 0;
  transform: scale(0.5);
}

.success-icon {
  color: #4ade80;
}

.error-icon {
  color: #ef4444;
}

.success-title, .error-title {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 1rem;
  color: #ffffff;
}

.success-message, .error-message {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  font-weight: 300;
}

.return-button, .retry-button {
  padding: 1rem 2.5rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: #ffffff;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }
}

// Animations
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes iconBounce {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  60% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes errorSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes circleFloat {
  0%, 100% {
    opacity: 0.03;
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    opacity: 0.08;
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes gridShift {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes cornerPulse {
  0%, 100% {
    opacity: 0.08;
    transform: scale(1);
  }
  50% {
    opacity: 0.15;
    transform: scale(1.05);
  }
}

@keyframes logoGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes badgeGlow {
  0% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

@keyframes textShimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .header {
    padding: 1.5rem 2rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .form-section {
    padding: 3rem 1.5rem; // Match home page mobile spacing pattern
  }

  .form-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .form-subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .wishlist-form {
    padding: 2.5rem 2rem;
  }

  .form-grid {
    gap: 1.8rem;
  }

  .form-input, .form-select {
    padding: 1.3rem 1.5rem; // Touch-friendly
    font-size: 16px; // Prevent zoom on iOS (must be exactly 16px)
    min-height: 52px; // Ensure adequate touch target
    position: relative;
    z-index: 5; // Ensure proper layering
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .submit-button {
    padding: 1.3rem 2.5rem;
    min-height: 52px; // Touch-friendly
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
  }

  .back-button {
    padding: 1.1rem 1.5rem;
    min-height: 48px; // Touch-friendly
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
  }

  .corner-element {
    width: 50px;
    height: 50px;

    &.top-left, &.top-right {
      top: 20px;
    }

    &.bottom-left, &.bottom-right {
      bottom: 20px;
    }

    &.top-left, &.bottom-left {
      left: 20px;
    }

    &.top-right, &.bottom-right {
      right: 20px;
    }
  }
}

// Small Mobile Optimization
@media (max-width: 480px) {
  .header {
    padding: 1rem 1.5rem;
  }

  .form-section {
    padding: 2rem 1rem; // Match home page small mobile spacing pattern
  }

  .form-header {
    margin-bottom: 2rem;
    padding: 0 0.5rem;
  }

  .form-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .form-subtitle {
    font-size: 0.95rem;
    padding: 0 0.5rem;
  }

  .wishlist-form {
    padding: 2rem 1.5rem;
  }

  .form-grid {
    gap: 1.5rem;
  }

  .form-input, .form-select {
    padding: 1.2rem 1.3rem; // Maintain touch-friendly size
    font-size: 16px; // Prevent zoom on iOS (must be exactly 16px)
    min-height: 50px; // Ensure adequate touch target
    position: relative;
    z-index: 5; // Ensure proper layering
    touch-action: manipulation; // Optimize for touch
    -webkit-appearance: none; // Remove iOS styling
    appearance: none;
  }

  .submit-button {
    padding: 1.2rem 2rem;
    font-size: 0.95rem;
    min-width: 180px;
    min-height: 48px; // Touch-friendly
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
  }

  .back-button {
    padding: 1rem 1.3rem;
    font-size: 0.85rem;
    min-height: 44px; // Touch-friendly
    position: relative;
    z-index: 10; // Ensure clickable
    touch-action: manipulation; // Optimize for touch
  }

  .corner-element {
    display: none;
  }
}

// Extra Small Mobile
@media (max-width: 320px) {
  .form-section {
    padding: 1.5rem 0.8rem; // Match home page extra small mobile spacing pattern
  }

  .wishlist-form {
    padding: 1.5rem 1.2rem;
  }

  .form-title {
    font-size: 1.8rem;
  }

  .form-input, .form-select {
    padding: 1rem 1.2rem;
  }

  .submit-button {
    min-width: 160px;
    padding: 1rem 1.5rem;
  }
}

// Performance optimizations
.form-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.form-input, .form-select, .submit-button, .back-button {
  will-change: transform, opacity;
  transform: translateZ(0);
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .submit-button:hover,
  .back-button:hover,
  .form-input:hover,
  .form-select:hover {
    transform: none; // Disable hover transforms on touch devices
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .geometric-bg {
    animation: none !important;

    .circle {
      animation: none !important;
    }

    .grid-pattern {
      animation: none !important;
    }
  }

  .corner-element {
    animation: none !important;
  }
}

// Performance optimizations
.wishlist-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.form-container, .form-input, .form-select {
  will-change: transform, opacity;
  transform: translateZ(0);
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}