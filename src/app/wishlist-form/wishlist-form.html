<div class="wishlist-container">
  <!-- Background geometric elements -->
  <div class="geometric-bg">
    <div class="circle circle-1"></div>
    <div class="circle circle-2"></div>
    <div class="circle circle-3"></div>
    <div class="grid-pattern"></div>
  </div>

  <!-- Shared Header -->
  <app-shared-header
    pageType="auth"
    appTitle="InspheraAI"
    [showNavigation]="true">
  </app-shared-header>

  <!-- Main Form Section -->
  <main class="form-section">
    <div class="form-container">
      <div class="form-header">
        <div class="form-badge">
          <span class="badge-text">Join the Future</span>
        </div>
        <h1 class="form-title">
          <span class="title-text">Join Our Wishlist</span>
        </h1>
        <p class="form-subtitle">
          Be among the first to experience next-generation agentic intelligence.
          Get exclusive early access and shape the future of AI-powered sales automation.
        </p>
      </div>

      <form class="wishlist-form" [formGroup]="wishlistForm" (ngSubmit)="onSubmit()" *ngIf="!isSubmitted">
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label" for="fullName">
              Full Name <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input
                type="text"
                id="fullName"
                class="form-input"
                formControlName="fullName"
                placeholder="Enter your full name"
                [class.error]="isFieldInvalid('fullName')"
                [class.success]="isFieldValid('fullName')"
              />
              <div class="input-glow"></div>
            </div>
            <div class="error-message" *ngIf="isFieldInvalid('fullName')">
              {{ getFieldError('fullName') }}
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="email">
              Email Address <span class="required">*</span>
            </label>
            <div class="input-wrapper">
              <input
                type="email"
                id="email"
                class="form-input"
                formControlName="email"
                placeholder="Enter your email address"
                [class.error]="isFieldInvalid('email')"
                [class.success]="isFieldValid('email')"
                autocomplete="email"
                inputmode="email"
              />
              <div class="input-glow"></div>
            </div>
            <div class="error-message" *ngIf="isFieldInvalid('email')">
              {{ getFieldError('email') }}
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="company">
              Company <span class="optional">(Optional)</span>
            </label>
            <div class="input-wrapper">
              <input
                type="text"
                id="company"
                class="form-input"
                formControlName="company"
                placeholder="Enter your company name"
                [class.success]="wishlistForm.get('company')?.value"
              />
              <div class="input-glow"></div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="businessType">
              Business Type <span class="required">*</span>
            </label>
            <div class="select-wrapper">
              <select
                id="businessType"
                class="form-select"
                formControlName="businessType"
                [class.error]="isFieldInvalid('businessType')"
                [class.success]="isFieldValid('businessType')"
              >
                <option value="">Select your business type</option>
                <option value="e-commerce">E-commerce & Retail</option>
                <option value="saas">SaaS & Technology</option>
                <option value="healthcare">Healthcare & Medical</option>
                <option value="finance">Finance & Banking</option>
                <option value="real-estate">Real Estate</option>
                <option value="education">Education & Training</option>
                <option value="consulting">Consulting & Professional Services</option>
                <option value="manufacturing">Manufacturing & Industrial</option>
                <option value="hospitality">Hospitality & Travel</option>
                <option value="nonprofit">Non-profit & Government</option>
                <option value="other">Other</option>
              </select>
              <div class="select-glow"></div>
            </div>
            <div class="error-message" *ngIf="isFieldInvalid('businessType')">
              {{ getFieldError('businessType') }}
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="submit"
            class="submit-button"
            [disabled]="wishlistForm.invalid || isLoading"
            [class.loading]="isLoading"
          >
            <span class="button-text" *ngIf="!isLoading">Join Wishlist</span>
            <span class="button-text" *ngIf="isLoading">Processing...</span>
            <div class="button-glow"></div>
            <div class="loading-spinner" *ngIf="isLoading"></div>
          </button>
        </div>
      </form>

      <!-- Success Message -->
      <div class="success-container" *ngIf="isSubmitted && !hasError">
        <div class="success-icon">✓</div>
        <h2 class="success-title">Welcome to the Future!</h2>
        <p class="success-message">
          Thank you for joining our wishlist! You'll be among the first to know when
          InspheraAI's next-generation agentic intelligence becomes available.
        </p>
        <button class="return-button" (click)="navigateBack()">
          <span class="button-text">Return to Home</span>
        </button>
      </div>

      <!-- Error Message -->
      <div class="error-container" *ngIf="hasError">
        <div class="error-icon">⚠</div>
        <h2 class="error-title">Something went wrong</h2>
        <p class="error-message">
          We couldn't process your request right now. Please try again later.
        </p>
        <button class="retry-button" (click)="resetForm()">
          <span class="button-text">Try Again</span>
        </button>
      </div>
    </div>
  </main>

  <!-- Corner decorative elements -->
  <div class="corner-elements">
    <div class="corner-element top-left"></div>
    <div class="corner-element top-right"></div>
    <div class="corner-element bottom-left"></div>
    <div class="corner-element bottom-right"></div>
  </div>
</div>
