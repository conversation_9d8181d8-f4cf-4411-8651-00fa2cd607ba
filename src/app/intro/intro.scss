.intro-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 70%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;

  // Animated background particles
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.05), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.08), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.03), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.06), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 20s linear infinite;
    opacity: 0;
    animation-delay: 1s;
    animation-fill-mode: forwards;
  }

  // Geometric pattern overlay
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(255, 255, 255, 0.03);
    border-radius: 50%;
    animation: geometricPulse 4s ease-in-out infinite;
  }
}

.content {
  text-align: center;
  animation: fadeUp 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(40px);
  position: relative;
  z-index: 10;
}

// Background geometric elements
.geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  animation: circleFloat 15s ease-in-out infinite;

  &.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 5s;
  }

  &.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 10s;
  }
}

// Corner decorative elements
.corner-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.corner-element {
  position: absolute;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  &.top-left {
    top: 30px;
    left: 30px;
    border-right: none;
    border-bottom: none;
    animation: cornerPulse 4s ease-in-out infinite;
  }

  &.top-right {
    top: 30px;
    right: 30px;
    border-left: none;
    border-bottom: none;
    animation: cornerPulse 4s ease-in-out infinite 1s;
  }

  &.bottom-left {
    bottom: 30px;
    left: 30px;
    border-right: none;
    border-top: none;
    animation: cornerPulse 4s ease-in-out infinite 2s;
  }

  &.bottom-right {
    bottom: 30px;
    right: 30px;
    border-left: none;
    border-top: none;
    animation: cornerPulse 4s ease-in-out infinite 3s;
  }
}

.logo-container {
  margin-bottom: 3rem;
  animation: logoFadeIn 1.2s ease-out 0.8s forwards;
  opacity: 0;
  position: relative;
  z-index: 10;
}

.logo-wrapper {
  position: relative;
  display: inline-block;

  // Enhanced glow effect behind logo
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 180px;
    height: 180px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 3s ease-in-out infinite alternate;
    z-index: -1;
  }

  // Secondary glow ring
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 220px;
    height: 220px;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    animation: logoRing 6s linear infinite;
    z-index: -1;
  }
}

.logo {
  max-width: 140px;
  height: auto;
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 25px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 50px rgba(255, 255, 255, 0.2))
    drop-shadow(0 0 75px rgba(255, 255, 255, 0.1));
  transition: filter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  // Removed logoFloat animation - logo is now static

  &:hover {
    // Removed transform scale - logo stays fixed in position
    filter:
      brightness(0)
      invert(1)
      drop-shadow(0 0 35px rgba(255, 255, 255, 0.6))
      drop-shadow(0 0 70px rgba(255, 255, 255, 0.3))
      drop-shadow(0 0 105px rgba(255, 255, 255, 0.15));
  }
}

.title {
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 4rem;
  font-weight: 200;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  letter-spacing: 0.15em;
  position: relative;
  z-index: 10;
}

.title-text {
  display: inline-block;
  text-shadow:
    0 0 30px rgba(255, 255, 255, 0.4),
    0 0 60px rgba(255, 255, 255, 0.2),
    0 0 90px rgba(255, 255, 255, 0.1);
  animation: titleGlow 3s ease-in-out infinite alternate;
  position: relative;

  // Enhanced underline effect
  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.8) 20%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(255, 255, 255, 0.8) 80%,
      transparent
    );
    transform: translateX(-50%);
    animation: underlineExpand 2s ease-out 1.5s forwards;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
}

.subtitle {
  margin-bottom: 3rem;
  opacity: 0;
  animation: subtitleFadeIn 1.5s ease-out 1.2s forwards;
}

.subtitle-text {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 1.1rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 0.06em;
  text-transform: uppercase;
  position: relative;
  display: inline-block;
  max-width: 90vw;
  text-align: center;
  line-height: 1.4;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  &::before {
    left: -60px;
  }

  &::after {
    right: -60px;
  }
}

.loading-section {
  position: relative;
  z-index: 10;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin-bottom: 2rem;
  position: relative;
}

.dot {
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 50%, #ffffff 100%);
  border-radius: 50%;
  animation: dotPulse 2.5s ease-in-out infinite;
  box-shadow:
    0 0 25px rgba(255, 255, 255, 0.7),
    0 0 50px rgba(255, 255, 255, 0.4),
    0 0 75px rgba(255, 255, 255, 0.2),
    inset 0 0 15px rgba(255, 255, 255, 0.3);
  position: relative;

  // Enhanced ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 2.5s ease-out infinite;
  }

  // Secondary ripple
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150%;
    height: 150%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: rippleSecondary 2.5s ease-out infinite;
  }

  &:nth-child(1) {
    animation-delay: 0s;
    &::before { animation-delay: 0s; }
    &::after { animation-delay: 0s; }
  }

  &:nth-child(2) {
    animation-delay: 0.5s;
    &::before { animation-delay: 0.5s; }
    &::after { animation-delay: 0.5s; }
  }

  &:nth-child(3) {
    animation-delay: 1s;
    &::before { animation-delay: 1s; }
    &::after { animation-delay: 1s; }
  }
}

.loading-text {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 0.9rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.1em;
  text-align: center;
  opacity: 0;
  animation: loadingTextFadeIn 1s ease-out 3s forwards;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    width: 30px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-50%);
  }
}

// Enhanced Animations
@keyframes fadeUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// logoFloat animation removed - logo is now static

@keyframes logoGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 30px rgba(255, 255, 255, 0.4),
      0 0 60px rgba(255, 255, 255, 0.2),
      0 0 90px rgba(255, 255, 255, 0.1);
  }
  100% {
    text-shadow:
      0 0 40px rgba(255, 255, 255, 0.6),
      0 0 80px rgba(255, 255, 255, 0.3),
      0 0 120px rgba(255, 255, 255, 0.15);
  }
}

@keyframes underlineExpand {
  0% {
    width: 0;
  }
  100% {
    width: 200px;
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.4);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2.5);
  }
}

@keyframes particleFloat {
  0% {
    opacity: 0;
    transform: translateY(0px);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(-100px);
  }
}

@keyframes geometricPulse {
  0%, 100% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.1) rotate(180deg);
  }
}

@keyframes circleFloat {
  0%, 100% {
    opacity: 0.05;
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    opacity: 0.15;
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes cornerPulse {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

@keyframes logoRing {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    opacity: 0.2;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
    opacity: 0.1;
  }
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingTextFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rippleSecondary {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(3);
  }
}

// Enhanced Responsive Design
@media (max-width: 1024px) {
  .title {
    font-size: 3.5rem;
    letter-spacing: 0.12em;
  }

  .logo {
    max-width: 120px;
  }

  .intro-container::after {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.8rem;
    letter-spacing: 0.1em;
    margin-bottom: 2.5rem;
  }

  .logo {
    max-width: 100px;
  }

  .logo-container {
    margin-bottom: 2.5rem;

    &::before {
      width: 120px;
      height: 120px;
    }
  }

  .subtitle-text {
    font-size: 1rem;
    letter-spacing: 0.05em;

    &::before,
    &::after {
      width: 30px;
    }

    &::before {
      left: -45px;
    }

    &::after {
      right: -45px;
    }
  }

  .loading-dots {
    gap: 0.8rem;
    margin-top: 2.5rem;
  }

  .dot {
    width: 10px;
    height: 10px;
  }

  .intro-container::after {
    width: 200px;
    height: 200px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2.2rem;
    letter-spacing: 0.08em;
    margin-bottom: 2rem;

    &::after {
      width: 150px;
    }
  }

  .logo {
    max-width: 80px;
  }

  .logo-container {
    margin-bottom: 2rem;

    &::before {
      width: 100px;
      height: 100px;
    }
  }

  .subtitle-text {
    font-size: 0.85rem;
    letter-spacing: 0.04em;
    line-height: 1.3;

    &::before,
    &::after {
      width: 20px;
    }

    &::before {
      left: -35px;
    }

    &::after {
      right: -35px;
    }
  }

  .loading-dots {
    gap: 0.6rem;
    margin-top: 2rem;
  }

  .dot {
    width: 8px;
    height: 8px;
  }

  .intro-container {
    &::before {
      background-size: 150px 75px;
    }

    &::after {
      width: 150px;
      height: 150px;
    }
  }
}

@media (max-width: 320px) {
  .title {
    font-size: 1.8rem;
    letter-spacing: 0.06em;
  }

  .logo {
    max-width: 60px;
  }

  .logo-container {
    margin-bottom: 1.5rem;
  }

  .subtitle-text {
    font-size: 0.75rem;
    letter-spacing: 0.03em;

    &::before,
    &::after {
      display: none; // Hide decorative lines on very small screens
    }
  }
}

// High DPI displays optimization
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo {
    filter:
      brightness(0)
      invert(1)
      drop-shadow(0 0 20px rgba(255, 255, 255, 0.5))
      drop-shadow(0 0 40px rgba(255, 255, 255, 0.25))
      drop-shadow(0 0 60px rgba(255, 255, 255, 0.1));
  }
}

// Performance optimizations
.intro-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.title-text, .dot {
  will-change: transform, opacity;
  transform: translateZ(0);
}

.logo {
  // Logo is static, only needs opacity optimization during fade-in
  transform: translateZ(0);
}

// Ensure logo is always visible with fallback
.logo {
  // Primary filter for logo visibility
  filter:
    brightness(0)
    invert(1)
    drop-shadow(0 0 25px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 50px rgba(255, 255, 255, 0.2))
    drop-shadow(0 0 75px rgba(255, 255, 255, 0.1));

  // Fallback for browsers that don't support filters well
  background-color: transparent;
  mix-blend-mode: screen;

  // Ensure crisp rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .intro-container::before,
  .intro-container::after,
  .logo,
  .title,
  .dot::before {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }

  .content {
    animation: fadeUpReduced 0.5s ease-out forwards;
  }

  .logo-container {
    animation: logoFadeInReduced 0.3s ease-out 0.2s forwards;
  }
}

@keyframes fadeUpReduced {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoFadeInReduced {
  to {
    opacity: 1;
  }
}