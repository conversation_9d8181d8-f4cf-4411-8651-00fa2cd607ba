import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-intro',
  imports: [],
  templateUrl: './intro.html',
  styleUrl: './intro.scss'
})
export class Intro implements OnInit, OnDestroy {
  private timeoutId?: number;

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Navigate to home page after 3.5 seconds
    this.timeoutId = window.setTimeout(() => {
      this.navigateToHome();
    }, 3500);
  }

  ngOnDestroy(): void {
    // Clean up timeout if component is destroyed early
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  private navigateToHome(): void {
    this.router.navigate(['/home']);
  }
}
