import { Routes } from '@angular/router';
import { Intro } from './intro/intro';
import { Home } from './home/<USER>';
import { WishlistForm } from './wishlist-form/wishlist-form';
import { Login } from './auth/login/login';
import { RegisterComponent } from './auth/register/register';
import { DashboardComponent } from './dashboard/dashboard.component';

export const routes: Routes = [
  { path: '', component: Intro },
  { path: 'home', component: Home },
  { path: 'wishlist', component: WishlistForm },
  { path: 'login', component: Login },
  { path: 'register', component: RegisterComponent },
  { path: 'dashboard', component: DashboardComponent },
  { path: 'dashboard/:tab', component: DashboardComponent },
  { path: '**', redirectTo: '' }
];
