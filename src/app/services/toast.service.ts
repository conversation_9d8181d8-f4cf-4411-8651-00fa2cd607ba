import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'register';
  title: string;
  message?: string;
  duration?: number;
  component?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toastsSubject = new BehaviorSubject<ToastMessage[]>([]);
  public toasts$ = this.toastsSubject.asObservable();

  constructor() {}

  showToast(toast: Omit<ToastMessage, 'id'>): string {
    const id = this.generateId();
    const newToast: ToastMessage = {
      id,
      duration: 5000, // Default 5 seconds
      ...toast
    };

    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);

    // Auto-remove toast after duration (unless it's a register toast)
    if (newToast.type !== 'register' && newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(id);
      }, newToast.duration);
    }

    return id;
  }

  removeToast(id: string): void {
    const currentToasts = this.toastsSubject.value;
    const filteredToasts = currentToasts.filter(toast => toast.id !== id);
    this.toastsSubject.next(filteredToasts);
  }

  showRegisterForm(): string {
    return this.showToast({
      type: 'register',
      title: 'Create Account',
      message: 'Complete your registration',
      duration: 0 // Don't auto-remove
    });
  }

  showSuccess(title: string, message?: string): string {
    return this.showToast({
      type: 'success',
      title,
      message
    });
  }

  showError(title: string, message?: string): string {
    return this.showToast({
      type: 'error',
      title,
      message
    });
  }

  clearAll(): void {
    this.toastsSubject.next([]);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
